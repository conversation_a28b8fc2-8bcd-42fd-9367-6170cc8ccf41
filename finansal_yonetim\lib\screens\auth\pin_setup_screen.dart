import 'package:flutter/material.dart';
import 'package:pin_code_fields/pin_code_fields.dart';
import 'package:provider/provider.dart';
import '../../services/security_service.dart';
import '../../services/email_service.dart';

class PinSetupScreen extends StatefulWidget {
  final VoidCallback onPinSet;

  const PinSetupScreen({super.key, required this.onPinSet});

  @override
  State<PinSetupScreen> createState() => _PinSetupScreenState();
}

class _PinSetupScreenState extends State<PinSetupScreen> {
  final TextEditingController _pinController = TextEditingController();
  final TextEditingController _confirmPinController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();
  
  String _currentPin = "";
  String _confirmPin = "";
  bool _isConfirming = false;
  bool _isLoading = false;
  bool _showEmailField = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Logo ve başlık
              Icon(
                Icons.security,
                size: 80,
                color: Theme.of(context).colorScheme.primary,
              ),
              const SizedBox(height: 24),
              Text(
                'Güvenlik PIN\'i Oluşturun',
                style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              Text(
                _isConfirming 
                    ? 'PIN\'inizi tekrar girin'
                    : 'Uygulamanızı korumak için 6 haneli bir PIN oluşturun',
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 48),
              
              // PIN giriş alanı
              PinCodeTextField(
                appContext: context,
                length: 6,
                controller: _isConfirming ? _confirmPinController : _pinController,
                obscureText: true,
                obscuringCharacter: '●',
                animationType: AnimationType.fade,
                pinTheme: PinTheme(
                  shape: PinCodeFieldShape.box,
                  borderRadius: BorderRadius.circular(8),
                  fieldHeight: 60,
                  fieldWidth: 50,
                  activeFillColor: Theme.of(context).colorScheme.primaryContainer,
                  inactiveFillColor: Theme.of(context).colorScheme.surfaceContainerHighest,
                  selectedFillColor: Theme.of(context).colorScheme.primaryContainer,
                  activeColor: Theme.of(context).colorScheme.primary,
                  inactiveColor: Theme.of(context).colorScheme.outline,
                  selectedColor: Theme.of(context).colorScheme.primary,
                ),
                enableActiveFill: true,
                onCompleted: (value) {
                  if (_isConfirming) {
                    _confirmPin = value;
                    _validateAndSetPin();
                  } else {
                    _currentPin = value;
                    setState(() {
                      _isConfirming = true;
                    });
                  }
                },
                onChanged: (value) {
                  if (_isConfirming) {
                    _confirmPin = value;
                  } else {
                    _currentPin = value;
                  }
                },
              ),
              
              const SizedBox(height: 32),
              
              // E-posta alanı (isteğe bağlı)
              if (_showEmailField) ...[
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'E-posta Adresi (İsteğe Bağlı)',
                          style: Theme.of(context).textTheme.titleMedium,
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'PIN\'inizi unutursanız sıfırlama kodu göndermek için',
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Theme.of(context).colorScheme.onSurfaceVariant,
                          ),
                        ),
                        const SizedBox(height: 16),
                        TextField(
                          controller: _emailController,
                          keyboardType: TextInputType.emailAddress,
                          decoration: InputDecoration(
                            labelText: 'E-posta',
                            hintText: '<EMAIL>',
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                            prefixIcon: const Icon(Icons.email),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 24),
              ],
              
              // Butonlar
              if (_isConfirming) ...[
                Row(
                  children: [
                    Expanded(
                      child: OutlinedButton(
                        onPressed: () {
                          setState(() {
                            _isConfirming = false;
                            _pinController.clear();
                            _confirmPinController.clear();
                            _currentPin = "";
                            _confirmPin = "";
                          });
                        },
                        child: const Text('Geri'),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: ElevatedButton(
                        onPressed: _confirmPin.length == 6 ? _validateAndSetPin : null,
                        child: _isLoading 
                            ? const SizedBox(
                                height: 20,
                                width: 20,
                                child: CircularProgressIndicator(strokeWidth: 2),
                              )
                            : const Text('Onayla'),
                      ),
                    ),
                  ],
                ),
              ] else ...[
                if (!_showEmailField)
                  TextButton.icon(
                    onPressed: () {
                      setState(() {
                        _showEmailField = true;
                      });
                    },
                    icon: const Icon(Icons.email),
                    label: const Text('E-posta Ekle (PIN Sıfırlama)'),
                  ),
              ],
              
              const SizedBox(height: 24),
              
              // Bilgi metni
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.surfaceContainerHighest,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.info_outline,
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        'PIN\'iniz güvenli bir şekilde şifrelenerek cihazınızda saklanır.',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _validateAndSetPin() async {
    if (_currentPin != _confirmPin) {
      _showErrorDialog('PIN\'ler eşleşmiyor', 'Lütfen aynı PIN\'i iki kez girin.');
      setState(() {
        _isConfirming = false;
        _pinController.clear();
        _confirmPinController.clear();
        _currentPin = "";
        _confirmPin = "";
      });
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final securityService = context.read<SecurityService>();
      final success = await securityService.setPIN(_currentPin);

      if (success) {
        // E-posta varsa kaydet
        if (_emailController.text.isNotEmpty) {
          final emailService = EmailService();
          if (emailService.isValidEmail(_emailController.text)) {
            await emailService.saveEmail(_emailController.text);
          }
        }

        widget.onPinSet();
      } else {
        _showErrorDialog('Hata', 'PIN ayarlanırken bir hata oluştu.');
      }
    } catch (e) {
      _showErrorDialog('Hata', 'Beklenmeyen bir hata oluştu.');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _showErrorDialog(String title, String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Tamam'),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _pinController.dispose();
    _confirmPinController.dispose();
    _emailController.dispose();
    super.dispose();
  }
}
