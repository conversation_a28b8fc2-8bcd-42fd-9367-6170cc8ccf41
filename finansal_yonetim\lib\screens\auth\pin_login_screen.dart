import 'package:flutter/material.dart';
import 'package:pin_code_fields/pin_code_fields.dart';
import 'package:provider/provider.dart';
import '../../services/security_service.dart';
import '../../services/email_service.dart';
import 'pin_reset_screen.dart';

class PinLoginScreen extends StatefulWidget {
  final VoidCallback onLoginSuccess;

  const PinLoginScreen({super.key, required this.onLoginSuccess});

  @override
  State<PinLoginScreen> createState() => _PinLoginScreenState();
}

class _PinLoginScreenState extends State<PinLoginScreen> {
  final TextEditingController _pinController = TextEditingController();
  String _currentPin = "";
  bool _isLoading = false;
  int _failedAttempts = 0;
  bool _canUseBiometric = false;
  int _pinFieldRebuildKey = 0;

  @override
  void initState() {
    super.initState();
    _checkBiometricAvailability();
  }

  Future<void> _checkBiometricAvailability() async {
    final securityService = context.read<SecurityService>();
    final isAvailable = await securityService.isBiometricAvailable();
    final isEnabled = await securityService.isBiometricEnabled();
    
    setState(() {
      _canUseBiometric = isAvailable && isEnabled;
    });

    // Biyometrik otomatik başlatma
    if (_canUseBiometric) {
      _authenticateWithBiometric();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Logo ve başlık
              Icon(
                Icons.lock,
                size: 80,
                color: Theme.of(context).colorScheme.primary,
              ),
              const SizedBox(height: 24),
              Text(
                'Finansal Yönetim',
                style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              Text(
                'Devam etmek için PIN\'inizi girin',
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 48),
              
              // PIN giriş alanı
              PinCodeTextField(
                key: ValueKey(_pinFieldRebuildKey),
                appContext: context,
                length: 6,
                controller: _pinController,
                obscureText: true,
                obscuringCharacter: '●',
                animationType: AnimationType.fade,
                pinTheme: PinTheme(
                  shape: PinCodeFieldShape.box,
                  borderRadius: BorderRadius.circular(8),
                  fieldHeight: 60,
                  fieldWidth: 50,
                  activeFillColor: Theme.of(context).colorScheme.primaryContainer,
                  inactiveFillColor: Theme.of(context).colorScheme.surfaceContainerHighest,
                  selectedFillColor: Theme.of(context).colorScheme.primaryContainer,
                  activeColor: Theme.of(context).colorScheme.primary,
                  inactiveColor: Theme.of(context).colorScheme.outline,
                  selectedColor: Theme.of(context).colorScheme.primary,
                  errorBorderColor: Theme.of(context).colorScheme.error,
                ),
                enableActiveFill: true,
                onCompleted: (value) {
                  _currentPin = value;
                  _verifyPin();
                },
                onChanged: (value) {
                  _currentPin = value;
                },
                beforeTextPaste: (text) {
                  return true;
                },
              ),
              
              const SizedBox(height: 32),
              
              // Hata mesajı
              if (_failedAttempts > 0)
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.errorContainer,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.error_outline,
                        color: Theme.of(context).colorScheme.onErrorContainer,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          'Yanlış PIN. ${3 - _failedAttempts} deneme hakkınız kaldı.',
                          style: TextStyle(
                            color: Theme.of(context).colorScheme.onErrorContainer,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              
              const SizedBox(height: 24),
              
              // Biyometrik giriş butonu
              if (_canUseBiometric)
                OutlinedButton.icon(
                  onPressed: _authenticateWithBiometric,
                  icon: const Icon(Icons.fingerprint),
                  label: const Text('Biyometrik Giriş'),
                ),
              
              const SizedBox(height: 16),
              
              // PIN sıfırlama butonu
              TextButton(
                onPressed: _showResetOptions,
                child: const Text('PIN\'imi Unuttum'),
              ),
              
              const SizedBox(height: 32),
              
              // Yükleme göstergesi
              if (_isLoading)
                const CircularProgressIndicator(),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _verifyPin() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final securityService = context.read<SecurityService>();
      final isValid = await securityService.verifyPIN(_currentPin);

      if (isValid) {
        widget.onLoginSuccess();
      } else {
        setState(() {
          _failedAttempts++;
          _currentPin = "";
          _pinFieldRebuildKey++; // PIN alanını yeniden oluştur
        });

        // PIN alanını temizle
        _pinController.clear();

        if (_failedAttempts >= 3) {
          _showLockoutDialog();
        }
      }
    } catch (e) {
      _showErrorDialog('Hata', 'PIN doğrulanırken bir hata oluştu.');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _authenticateWithBiometric() async {
    try {
      final securityService = context.read<SecurityService>();
      final isAuthenticated = await securityService.authenticateWithBiometrics();

      if (isAuthenticated) {
        widget.onLoginSuccess();
      }
    } catch (e) {
      _showErrorDialog('Biyometrik Hata', 'Biyometrik kimlik doğrulama başarısız.');
    }
  }

  void _showResetOptions() async {
    final emailService = EmailService();
    final hasEmail = await emailService.isEmailRegistered();

    if (!hasEmail) {
      _showErrorDialog(
        'E-posta Bulunamadı',
        'PIN sıfırlama için kayıtlı e-posta adresiniz bulunmuyor. Uygulamayı yeniden yükleyip yeni PIN oluşturmanız gerekebilir.',
      );
      return;
    }

    if (mounted) {
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => PinResetScreen(
            onResetSuccess: () {
              if (mounted) {
                Navigator.of(context).pop();
                setState(() {
                  _failedAttempts = 0;
                });
              }
            },
          ),
        ),
      );
    }
  }

  void _showLockoutDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('Hesap Kilitlendi'),
        content: const Text(
          '3 kez yanlış PIN girdiniz. Güvenlik nedeniyle hesabınız geçici olarak kilitlendi. '
          'PIN\'inizi sıfırlamak için "PIN\'imi Unuttum" seçeneğini kullanın.',
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _showResetOptions();
            },
            child: const Text('PIN Sıfırla'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              setState(() {
                _failedAttempts = 0;
              });
            },
            child: const Text('Tekrar Dene'),
          ),
        ],
      ),
    );
  }

  void _showErrorDialog(String title, String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Tamam'),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _pinController.dispose();
    super.dispose();
  }
}
