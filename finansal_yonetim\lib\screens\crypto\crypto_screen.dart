import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../services/database_service.dart';
import '../../services/crypto_service.dart';
import '../../models/crypto_asset.dart';

class CryptoScreen extends StatefulWidget {
  const CryptoScreen({super.key});

  @override
  State<CryptoScreen> createState() => _CryptoScreenState();
}

class _CryptoScreenState extends State<CryptoScreen> {
  List<CryptoAsset> _cryptoAssets = [];
  bool _isLoading = true;
  bool _isUpdatingPrices = false;

  @override
  void initState() {
    super.initState();
    _loadCryptoAssets();
  }

  Future<void> _loadCryptoAssets() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final dbService = context.read<DatabaseService>();
      final assets = await dbService.getAllCryptoAssets();
      
      setState(() {
        _cryptoAssets = assets;
        _isLoading = false;
      });

      // Fiyatları güncelle
      if (assets.isNotEmpty) {
        _updatePrices();
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _updatePrices() async {
    if (_cryptoAssets.isEmpty) return;

    setState(() {
      _isUpdatingPrices = true;
    });

    try {
      final cryptoService = CryptoService();
      final dbService = context.read<DatabaseService>();
      
      for (var asset in _cryptoAssets) {
        final cryptoId = cryptoService.getCryptoIdBySymbol(asset.symbol);
        if (cryptoId != null) {
          final currentPrice = await cryptoService.getCryptoPrice(cryptoId);
          if (currentPrice != null) {
            final updatedAsset = asset.copyWith(
              currentPrice: currentPrice,
              updatedAt: DateTime.now(),
            );
            await dbService.updateCryptoAsset(updatedAsset);
          }
        }
      }

      _loadCryptoAssets();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Fiyatlar güncellenirken hata oluştu')),
        );
      }
    } finally {
      setState(() {
        _isUpdatingPrices = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final totalValue = _cryptoAssets.fold<double>(0, (sum, asset) => sum + (asset.amount * asset.currentPrice));

    return Scaffold(
      backgroundColor: const Color(0xFFF8FAFC),
      body: CustomScrollView(
        slivers: [
          // Muhteşem AppBar
          SliverAppBar(
            expandedHeight: 300,
            floating: false,
            pinned: true,
            elevation: 0,
            backgroundColor: Colors.transparent,
            flexibleSpace: FlexibleSpaceBar(
              background: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      const Color(0xFFEA580C), // Orange
                      const Color(0xFFF59E0B), // Amber
                      const Color(0xFFFBBF24), // Yellow
                      const Color(0xFFEAB308), // Gold
                    ],
                    stops: const [0.0, 0.4, 0.8, 1.0],
                  ),
                ),
                child: SafeArea(
                  child: Padding(
                    padding: const EdgeInsets.all(24.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Başlık ve aksiyon butonları
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Container(
                                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                                  decoration: BoxDecoration(
                                    color: Colors.white.withValues(alpha: 0.15),
                                    borderRadius: BorderRadius.circular(20),
                                    border: Border.all(
                                      color: Colors.white.withValues(alpha: 0.3),
                                      width: 1,
                                    ),
                                  ),
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Icon(
                                        Icons.currency_bitcoin_outlined,
                                        color: Colors.white.withValues(alpha: 0.9),
                                        size: 16,
                                      ),
                                      const SizedBox(width: 6),
                                      Text(
                                        'Kripto Varlıklar',
                                        style: TextStyle(
                                          color: Colors.white.withValues(alpha: 0.9),
                                          fontSize: 12,
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                const SizedBox(height: 16),
                                Text(
                                  'Dijital Portföyünüz',
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontSize: 28,
                                    fontWeight: FontWeight.w800,
                                    letterSpacing: -0.5,
                                  ),
                                ),
                              ],
                            ),
                            Row(
                              children: [
                                // Refresh butonu
                                Container(
                                  padding: const EdgeInsets.all(12),
                                  decoration: BoxDecoration(
                                    color: Colors.white.withValues(alpha: 0.15),
                                    borderRadius: BorderRadius.circular(16),
                                    border: Border.all(
                                      color: Colors.white.withValues(alpha: 0.3),
                                      width: 1,
                                    ),
                                  ),
                                  child: _isUpdatingPrices
                                      ? SizedBox(
                                          width: 20,
                                          height: 20,
                                          child: CircularProgressIndicator(
                                            color: Colors.white,
                                            strokeWidth: 2,
                                          ),
                                        )
                                      : InkWell(
                                          onTap: _updatePrices,
                                          child: Icon(
                                            Icons.refresh,
                                            color: Colors.white,
                                            size: 20,
                                          ),
                                        ),
                                ),
                                const SizedBox(width: 12),
                                // Add butonu
                                Container(
                                  padding: const EdgeInsets.all(12),
                                  decoration: BoxDecoration(
                                    color: Colors.white.withValues(alpha: 0.15),
                                    borderRadius: BorderRadius.circular(16),
                                    border: Border.all(
                                      color: Colors.white.withValues(alpha: 0.3),
                                      width: 1,
                                    ),
                                  ),
                                  child: InkWell(
                                    onTap: _showAddCryptoDialog,
                                    child: Icon(
                                      Icons.add,
                                      color: Colors.white,
                                      size: 20,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),

                        const Spacer(),

                        // Toplam değer ve istatistikler
                        Container(
                          padding: const EdgeInsets.all(24),
                          decoration: BoxDecoration(
                            color: Colors.white.withValues(alpha: 0.15),
                            borderRadius: BorderRadius.circular(20),
                            border: Border.all(
                              color: Colors.white.withValues(alpha: 0.3),
                              width: 1.5,
                            ),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withValues(alpha: 0.1),
                                blurRadius: 20,
                                offset: const Offset(0, 8),
                              ),
                            ],
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  Icon(
                                    Icons.account_balance_wallet_outlined,
                                    color: Colors.white.withValues(alpha: 0.9),
                                    size: 20,
                                  ),
                                  const SizedBox(width: 8),
                                  Text(
                                    'Toplam Değer',
                                    style: TextStyle(
                                      color: Colors.white.withValues(alpha: 0.9),
                                      fontSize: 14,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 12),
                              if (_isLoading)
                                Row(
                                  children: [
                                    SizedBox(
                                      width: 20,
                                      height: 20,
                                      child: CircularProgressIndicator(
                                        color: Colors.white,
                                        strokeWidth: 2,
                                      ),
                                    ),
                                    const SizedBox(width: 12),
                                    Text(
                                      'Hesaplanıyor...',
                                      style: TextStyle(
                                        color: Colors.white.withValues(alpha: 0.8),
                                        fontSize: 16,
                                      ),
                                    ),
                                  ],
                                )
                              else
                                Row(
                                  children: [
                                    Text(
                                      '₺',
                                      style: TextStyle(
                                        color: Colors.white,
                                        fontSize: 24,
                                        fontWeight: FontWeight.w700,
                                      ),
                                    ),
                                    const SizedBox(width: 4),
                                    Text(
                                      totalValue.toStringAsFixed(2),
                                      style: TextStyle(
                                        color: Colors.white,
                                        fontSize: 32,
                                        fontWeight: FontWeight.w800,
                                        letterSpacing: -0.5,
                                      ),
                                    ),
                                    const Spacer(),
                                    Container(
                                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                                      decoration: BoxDecoration(
                                        color: const Color(0xFF10B981).withValues(alpha: 0.2),
                                        borderRadius: BorderRadius.circular(12),
                                        border: Border.all(
                                          color: const Color(0xFF10B981).withValues(alpha: 0.4),
                                          width: 1,
                                        ),
                                      ),
                                      child: Text(
                                        '${_cryptoAssets.length} varlık',
                                        style: TextStyle(
                                          color: Colors.white,
                                          fontSize: 12,
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                            ],
                          ),
                        ),
                        const SizedBox(height: 20),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),

          // İçerik
          SliverPadding(
            padding: const EdgeInsets.all(20),
            sliver: _isLoading
                ? SliverToBoxAdapter(
                    child: Center(
                      child: Container(
                        padding: const EdgeInsets.all(40),
                        child: CircularProgressIndicator(
                          color: const Color(0xFFF59E0B),
                          strokeWidth: 3,
                        ),
                      ),
                    ),
                  )
                : _cryptoAssets.isEmpty
                    ? SliverToBoxAdapter(child: _buildEmptyState())
                    : _buildCryptoList(),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.currency_bitcoin_outlined,
            size: 80,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
          const SizedBox(height: 16),
          Text(
            'Henüz kripto varlık yok',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 8),
          Text(
            'İlk kripto yatırımınızı ekleyin',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: _showAddCryptoDialog,
            icon: const Icon(Icons.add),
            label: const Text('Kripto Ekle'),
          ),
        ],
      ),
    );
  }

  Widget _buildCryptoList() {
    double totalValue = _cryptoAssets.fold(0.0, (sum, asset) => sum + asset.totalValue);
    double totalInvestment = _cryptoAssets.fold(0.0, (sum, asset) => sum + asset.totalInvestment);
    double totalProfitLoss = totalValue - totalInvestment;
    double totalProfitLossPercentage = totalInvestment > 0 ? (totalProfitLoss / totalInvestment) * 100 : 0;

    return RefreshIndicator(
      onRefresh: _loadCryptoAssets,
      child: Column(
        children: [
          // Toplam özet
          Container(
            margin: const EdgeInsets.all(16),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Theme.of(context).colorScheme.primaryContainer,
                  Theme.of(context).colorScheme.secondaryContainer,
                ],
              ),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              children: [
                Text(
                  'Toplam Portföy Değeri',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                const SizedBox(height: 8),
                Text(
                  '\$${totalValue.toStringAsFixed(2)}',
                  style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      '${totalProfitLoss >= 0 ? '+' : ''}\$${totalProfitLoss.toStringAsFixed(2)}',
                      style: TextStyle(
                        color: totalProfitLoss >= 0 ? Colors.green : Colors.red,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      '(${totalProfitLoss >= 0 ? '+' : ''}${totalProfitLossPercentage.toStringAsFixed(2)}%)',
                      style: TextStyle(
                        color: totalProfitLoss >= 0 ? Colors.green : Colors.red,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          
          // Kripto listesi
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              itemCount: _cryptoAssets.length,
              itemBuilder: (context, index) {
                final asset = _cryptoAssets[index];
                final isProfit = asset.profitLoss >= 0;
                
                return Card(
                  margin: const EdgeInsets.only(bottom: 12),
                  child: ListTile(
                    leading: CircleAvatar(
                      backgroundColor: Theme.of(context).colorScheme.primaryContainer,
                      child: Text(
                        asset.symbol.substring(0, 2),
                        style: TextStyle(
                          color: Theme.of(context).colorScheme.onPrimaryContainer,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    title: Text('${asset.name} (${asset.symbol})'),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text('${asset.amount} ${asset.symbol}'),
                        Text(
                          'Alış: \$${asset.purchasePrice.toStringAsFixed(4)}',
                          style: Theme.of(context).textTheme.bodySmall,
                        ),
                      ],
                    ),
                    trailing: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Text(
                          '\$${asset.totalValue.toStringAsFixed(2)}',
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          '\$${asset.currentPrice.toStringAsFixed(4)}',
                          style: Theme.of(context).textTheme.bodySmall,
                        ),
                        Text(
                          '${isProfit ? '+' : ''}${asset.profitLossPercentage.toStringAsFixed(1)}%',
                          style: TextStyle(
                            color: isProfit ? Colors.green : Colors.red,
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    onTap: () => _showCryptoDetails(asset),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  void _showAddCryptoDialog() {
    showDialog(
      context: context,
      builder: (context) => _AddCryptoDialog(
        onCryptoAdded: _loadCryptoAssets,
      ),
    );
  }

  void _showCryptoDetails(CryptoAsset asset) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('${asset.name} (${asset.symbol})'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildDetailRow('Miktar', '${asset.amount} ${asset.symbol}'),
            _buildDetailRow('Alış Fiyatı', '\$${asset.purchasePrice.toStringAsFixed(4)}'),
            _buildDetailRow('Güncel Fiyat', '\$${asset.currentPrice.toStringAsFixed(4)}'),
            _buildDetailRow('Toplam Yatırım', '\$${asset.totalInvestment.toStringAsFixed(2)}'),
            _buildDetailRow('Güncel Değer', '\$${asset.totalValue.toStringAsFixed(2)}'),
            _buildDetailRow('Kar/Zarar', '\$${asset.profitLoss.toStringAsFixed(2)}'),
            _buildDetailRow('Kar/Zarar %', '${asset.profitLossPercentage.toStringAsFixed(2)}%'),
            _buildDetailRow('Alış Tarihi', asset.purchaseDate.toString().split(' ')[0]),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Kapat'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _deleteCrypto(asset);
            },
            child: Text(
              'Sil',
              style: TextStyle(color: Theme.of(context).colorScheme.error),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }

  Future<void> _deleteCrypto(CryptoAsset asset) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Kripto Varlığı Sil'),
        content: Text('${asset.symbol} varlığını silmek istediğinizden emin misiniz?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('İptal'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.error,
            ),
            child: const Text('Sil'),
          ),
        ],
      ),
    );

    if (confirmed == true && asset.id != null && mounted) {
      try {
        final dbService = context.read<DatabaseService>();
        await dbService.deleteCryptoAsset(asset.id!);
        if (mounted) {
          _loadCryptoAssets();
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Kripto varlık başarıyla silindi')),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Kripto varlık silinirken hata oluştu')),
          );
        }
      }
    }
  }
}

class _AddCryptoDialog extends StatefulWidget {
  final VoidCallback onCryptoAdded;

  const _AddCryptoDialog({required this.onCryptoAdded});

  @override
  State<_AddCryptoDialog> createState() => _AddCryptoDialogState();
}

class _AddCryptoDialogState extends State<_AddCryptoDialog> {
  final TextEditingController _amountController = TextEditingController();
  final TextEditingController _priceController = TextEditingController();
  String _selectedCrypto = 'BTC';
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Kripto Varlık Ekle'),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            DropdownButtonFormField<String>(
              value: _selectedCrypto,
              decoration: const InputDecoration(
                labelText: 'Kripto Para',
                border: OutlineInputBorder(),
              ),
              items: CryptoService.popularCryptos
                  .map((crypto) => DropdownMenuItem(
                        value: crypto['symbol'],
                        child: Text('${crypto['name']} (${crypto['symbol']})'),
                      ))
                  .toList(),
              onChanged: (value) {
                setState(() {
                  _selectedCrypto = value!;
                });
              },
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _amountController,
              keyboardType: TextInputType.number,
              decoration: const InputDecoration(
                labelText: 'Miktar',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _priceController,
              keyboardType: TextInputType.number,
              decoration: const InputDecoration(
                labelText: 'Alış Fiyatı (USD)',
                border: OutlineInputBorder(),
              ),
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('İptal'),
        ),
        ElevatedButton(
          onPressed: _isLoading ? null : _addCrypto,
          child: _isLoading
              ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : const Text('Ekle'),
        ),
      ],
    );
  }

  Future<void> _addCrypto() async {
    final amount = double.tryParse(_amountController.text);
    final price = double.tryParse(_priceController.text);

    if (amount == null || price == null || amount <= 0 || price <= 0) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Lütfen geçerli değerler girin')),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final cryptoService = CryptoService();
      final cryptoName = cryptoService.getCryptoNameBySymbol(_selectedCrypto) ?? _selectedCrypto;
      
      // Güncel fiyatı al
      final cryptoId = cryptoService.getCryptoIdBySymbol(_selectedCrypto);
      double currentPrice = price; // Varsayılan olarak alış fiyatı
      
      if (cryptoId != null) {
        final fetchedPrice = await cryptoService.getCryptoPrice(cryptoId);
        if (fetchedPrice != null) {
          currentPrice = fetchedPrice;
        }
      }

      final asset = CryptoAsset(
        symbol: _selectedCrypto,
        name: cryptoName,
        amount: amount,
        purchasePrice: price,
        currentPrice: currentPrice,
        purchaseDate: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      if (mounted) {
        final dbService = context.read<DatabaseService>();
        await dbService.insertCryptoAsset(asset);

        if (mounted) {
          Navigator.of(context).pop();
          widget.onCryptoAdded();

          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Kripto varlık başarıyla eklendi')),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Kripto varlık eklenirken hata oluştu')),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  void dispose() {
    _amountController.dispose();
    _priceController.dispose();
    super.dispose();
  }
}
