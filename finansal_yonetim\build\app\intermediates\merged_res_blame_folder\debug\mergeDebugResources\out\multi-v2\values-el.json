{"logs": [{"outputFile": "com.example.finansal.finansal_yonetim.app-mergeDebugResources-41:/values-el/values-el.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\cceca150324ee75eadce8003b387b1fb\\transformed\\biometric-1.1.0\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,172,263,383,535,688,828,966,1119,1222,1365,1512", "endColumns": "116,90,119,151,152,139,137,152,102,142,146,132", "endOffsets": "167,258,378,530,683,823,961,1114,1217,1360,1507,1640"}, "to": {"startLines": "36,38,40,41,42,43,44,45,46,47,48,49", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3621,3811,3989,4109,4261,4414,4554,4692,4845,4948,5091,5238", "endColumns": "116,90,119,151,152,139,137,152,102,142,146,132", "endOffsets": "3733,3897,4104,4256,4409,4549,4687,4840,4943,5086,5233,5366"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\66aa7f682cf61ffe3ee75db6ee238d77\\transformed\\core-1.13.1\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,356,459,567,673,790", "endColumns": "97,102,99,102,107,105,116,100", "endOffsets": "148,251,351,454,562,668,785,886"}, "to": {"startLines": "29,30,31,32,33,34,35,53", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2886,2984,3087,3187,3290,3398,3504,5682", "endColumns": "97,102,99,102,107,105,116,100", "endOffsets": "2979,3082,3182,3285,3393,3499,3616,5778"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\a295c1332cd792405fffabf7b4bbac54\\transformed\\appcompat-1.2.0\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,223,334,451,536,642,765,854,939,1030,1123,1218,1312,1412,1505,1600,1697,1788,1879,1964,2075,2184,2286,2397,2507,2615,2786,2886", "endColumns": "117,110,116,84,105,122,88,84,90,92,94,93,99,92,94,96,90,90,84,110,108,101,110,109,107,170,99,85", "endOffsets": "218,329,446,531,637,760,849,934,1025,1118,1213,1307,1407,1500,1595,1692,1783,1874,1959,2070,2179,2281,2392,2502,2610,2781,2881,2967"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,52", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,223,334,451,536,642,765,854,939,1030,1123,1218,1312,1412,1505,1600,1697,1788,1879,1964,2075,2184,2286,2397,2507,2615,2786,5596", "endColumns": "117,110,116,84,105,122,88,84,90,92,94,93,99,92,94,96,90,90,84,110,108,101,110,109,107,170,99,85", "endOffsets": "218,329,446,531,637,760,849,934,1025,1118,1213,1307,1407,1500,1595,1692,1783,1874,1959,2070,2179,2281,2392,2502,2610,2781,2881,5677"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\b83b8b00b8346c9e7414a1f1298f055d\\transformed\\preference-1.2.1\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,178,265,348,490,659,744", "endColumns": "72,86,82,141,168,84,79", "endOffsets": "173,260,343,485,654,739,819"}, "to": {"startLines": "37,39,50,51,54,55,56", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "3738,3902,5371,5454,5783,5952,6037", "endColumns": "72,86,82,141,168,84,79", "endOffsets": "3806,3984,5449,5591,5947,6032,6112"}}]}]}