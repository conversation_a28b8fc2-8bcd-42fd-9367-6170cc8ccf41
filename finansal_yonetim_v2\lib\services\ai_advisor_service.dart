import 'package:flutter/foundation.dart';
import 'package:google_generative_ai/google_generative_ai.dart';
import '../models/bank_account.dart';
import '../models/crypto_asset.dart';
import '../models/transaction.dart';

class AIAdvice {
  final String title;
  final String content;
  final String category;
  final int priority; // 1-5 (5 en yüksek)
  final DateTime createdAt;

  AIAdvice({
    required this.title,
    required this.content,
    required this.category,
    required this.priority,
    DateTime? createdAt,
  }) : createdAt = createdAt ?? DateTime.now();
}

class AIAdvisorService extends ChangeNotifier {
  late final GenerativeModel _model;
  bool _isLoading = false;
  String? _error;
  List<AIAdvice> _advices = [];

  bool get isLoading => _isLoading;
  String? get error => _error;
  List<AIAdvice> get advices => _advices;

  AIAdvisorService() {
    // Demo için basit AI simülasyonu - gerçek uygulamada Gemini API kullanın
    const apiKey = 'demo_key';
    
    // Demo modu için model oluşturmayı atla
    if (apiKey != 'demo_key') {
      _model = GenerativeModel(
        model: 'gemini-pro',
        apiKey: apiKey,
        generationConfig: GenerationConfig(
          temperature: 0.7,
          topK: 40,
          topP: 0.95,
          maxOutputTokens: 1024,
        ),
      );
    }
  }

  // Finansal analiz ve tavsiye üret
  Future<void> generateFinancialAdvice({
    required List<BankAccount> bankAccounts,
    required List<CryptoAsset> cryptoAssets,
    required List<TransactionModel> recentTransactions,
  }) async {
    try {
      _isLoading = true;
      _error = null;
      notifyListeners();

      // Finansal durumu analiz et
      final analysis = _analyzeFinancialSituation(
        bankAccounts,
        cryptoAssets,
        recentTransactions,
      );

      // Demo modu - varsayılan tavsiyeler kullan
      _generateDefaultAdvices();
    } catch (e) {
      _error = 'AI tavsiyesi alınamadı: ${e.toString()}';
      debugPrint('AI Advisor Error: $e');
      
      // Hata durumunda varsayılan tavsiyeler
      _generateDefaultAdvices();
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Finansal durumu analiz et
  Map<String, dynamic> _analyzeFinancialSituation(
    List<BankAccount> bankAccounts,
    List<CryptoAsset> cryptoAssets,
    List<TransactionModel> recentTransactions,
  ) {
    final totalBankBalance = bankAccounts.fold<double>(
      0.0,
      (sum, account) => sum + account.balance,
    );

    final totalCryptoValue = cryptoAssets.fold<double>(
      0.0,
      (sum, asset) => sum + asset.totalValue,
    );

    final monthlyIncome = recentTransactions
        .where((t) => 
            t.type == TransactionType.income &&
            t.date.isAfter(DateTime.now().subtract(const Duration(days: 30))))
        .fold<double>(0.0, (sum, t) => sum + t.amount);

    final monthlyExpenses = recentTransactions
        .where((t) => 
            t.type == TransactionType.expense &&
            t.date.isAfter(DateTime.now().subtract(const Duration(days: 30))))
        .fold<double>(0.0, (sum, t) => sum + t.amount);

    final cryptoProfitLoss = cryptoAssets.fold<double>(
      0.0,
      (sum, asset) => sum + asset.profitLoss,
    );

    return {
      'totalBankBalance': totalBankBalance,
      'totalCryptoValue': totalCryptoValue,
      'totalWealth': totalBankBalance + totalCryptoValue,
      'monthlyIncome': monthlyIncome,
      'monthlyExpenses': monthlyExpenses,
      'monthlySavings': monthlyIncome - monthlyExpenses,
      'cryptoProfitLoss': cryptoProfitLoss,
      'bankAccountCount': bankAccounts.length,
      'cryptoAssetCount': cryptoAssets.length,
      'savingsRate': monthlyIncome > 0 ? ((monthlyIncome - monthlyExpenses) / monthlyIncome) * 100 : 0,
    };
  }

  // AI prompt'u oluştur
  String _buildPrompt(Map<String, dynamic> analysis) {
    return '''
Sen bir uzman finansal danışmansın. Aşağıdaki finansal verileri analiz ederek Türkçe tavsiyeler ver:

Finansal Durum:
- Toplam Banka Bakiyesi: ${analysis['totalBankBalance']} TL
- Toplam Kripto Değeri: ${analysis['totalCryptoValue']} TL
- Toplam Servet: ${analysis['totalWealth']} TL
- Aylık Gelir: ${analysis['monthlyIncome']} TL
- Aylık Gider: ${analysis['monthlyExpenses']} TL
- Aylık Tasarruf: ${analysis['monthlySavings']} TL
- Kripto Kar/Zarar: ${analysis['cryptoProfitLoss']} TL
- Tasarruf Oranı: %${analysis['savingsRate'].toStringAsFixed(1)}

Lütfen aşağıdaki kategorilerde kısa ve öz tavsiyeler ver:
1. BÜTÇE YÖNETİMİ
2. YATIRIM STRATEJİSİ  
3. RİSK YÖNETİMİ
4. TASARRUF ÖNERİLERİ

Her tavsiye için:
- Başlık (maksimum 50 karakter)
- İçerik (maksimum 200 karakter)
- Kategori (BUDGET, INVESTMENT, RISK, SAVINGS)
- Öncelik (1-5)

Format: BAŞLIK|İÇERİK|KATEGORİ|ÖNCELİK
''';
  }

  // AI yanıtını parse et ve tavsiyeleri sakla
  void _parseAndStoreAdvices(String response) {
    final lines = response.split('\n');
    _advices.clear();

    for (final line in lines) {
      if (line.contains('|')) {
        final parts = line.split('|');
        if (parts.length >= 4) {
          try {
            _advices.add(AIAdvice(
              title: parts[0].trim(),
              content: parts[1].trim(),
              category: parts[2].trim(),
              priority: int.tryParse(parts[3].trim()) ?? 3,
            ));
          } catch (e) {
            debugPrint('Parse advice error: $e');
          }
        }
      }
    }

    // Eğer parse edilemezse varsayılan tavsiyeler
    if (_advices.isEmpty) {
      _generateDefaultAdvices();
    }
  }

  // Varsayılan tavsiyeler
  void _generateDefaultAdvices() {
    _advices = [
      AIAdvice(
        title: '50/30/20 Kuralını Uygula',
        content: 'Gelirinizin %50\'sini ihtiyaçlar, %30\'unu istekler, %20\'sini tasarruf için ayırın.',
        category: 'BUDGET',
        priority: 5,
      ),
      AIAdvice(
        title: 'Acil Durum Fonu Oluştur',
        content: '6 aylık giderinizi karşılayacak acil durum fonu oluşturun.',
        category: 'SAVINGS',
        priority: 5,
      ),
      AIAdvice(
        title: 'Portföyünüzü Çeşitlendirin',
        content: 'Yatırımlarınızı farklı varlık sınıflarına dağıtarak riski azaltın.',
        category: 'INVESTMENT',
        priority: 4,
      ),
      AIAdvice(
        title: 'Kripto Yatırımlarını Sınırlayın',
        content: 'Kripto yatırımlarınız toplam portföyünüzün %10\'unu geçmemelidir.',
        category: 'RISK',
        priority: 4,
      ),
      AIAdvice(
        title: 'Aylık Giderlerinizi Takip Edin',
        content: 'Her ay giderlerinizi kategorilere ayırarak analiz edin.',
        category: 'BUDGET',
        priority: 3,
      ),
    ];
  }

  // Belirli bir konuda tavsiye al
  Future<String?> getSpecificAdvice(String question) async {
    try {
      _isLoading = true;
      notifyListeners();

      final prompt = '''
Sen bir uzman finansal danışmansın. Aşağıdaki soruya Türkçe, kısa ve öz bir şekilde cevap ver:

Soru: $question

Cevabın maksimum 300 karakter olsun ve pratik bir tavsiye içersin.
''';

      final response = await _model.generateContent([Content.text(prompt)]);
      return response.text;
    } catch (e) {
      _error = 'Tavsiye alınamadı: ${e.toString()}';
      return null;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Harcama analizi
  Future<String?> analyzeSpending(List<TransactionModel> transactions) async {
    try {
      final expensesByCategory = <String, double>{};
      
      for (final transaction in transactions) {
        if (transaction.type == TransactionType.expense) {
          final category = transaction.categoryName;
          expensesByCategory[category] = 
              (expensesByCategory[category] ?? 0) + transaction.amount;
        }
      }

      final sortedExpenses = expensesByCategory.entries.toList()
        ..sort((a, b) => b.value.compareTo(a.value));

      final prompt = '''
Aşağıdaki harcama verilerini analiz ederek Türkçe tavsiye ver:

${sortedExpenses.map((e) => '${e.key}: ${e.value.toStringAsFixed(2)} TL').join('\n')}

Hangi kategorilerde tasarruf yapılabileceğini ve nasıl yapılacağını kısa ve öz şekilde açıkla.
''';

      final response = await _model.generateContent([Content.text(prompt)]);
      return response.text;
    } catch (e) {
      return 'Harcama analizi yapılamadı.';
    }
  }

  // Tavsiyeleri kategoriye göre filtrele
  List<AIAdvice> getAdvicesByCategory(String category) {
    return _advices.where((advice) => advice.category == category).toList();
  }

  // Yüksek öncelikli tavsiyeleri getir
  List<AIAdvice> getHighPriorityAdvices() {
    return _advices.where((advice) => advice.priority >= 4).toList();
  }

  // Tavsiyeleri temizle
  void clearAdvices() {
    _advices.clear();
    _error = null;
    notifyListeners();
  }
}
