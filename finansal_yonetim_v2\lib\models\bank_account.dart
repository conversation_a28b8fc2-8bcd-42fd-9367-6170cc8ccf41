class BankAccount {
  final int? id;
  final String bankName;
  final String accountType;
  final String accountNumber;
  final String? iban;
  final double balance;
  final String currency;
  final String? cardNumber;
  final String? cardHolderName;
  final String? expiryDate;
  final String? cvv;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;

  BankAccount({
    this.id,
    required this.bankName,
    required this.accountType,
    required this.accountNumber,
    this.iban,
    this.balance = 0.0,
    this.currency = 'TRY',
    this.cardNumber,
    this.cardHolderName,
    this.expiryDate,
    this.cvv,
    this.isActive = true,
    DateTime? createdAt,
    DateTime? updatedAt,
  })  : createdAt = createdAt ?? DateTime.now(),
        updatedAt = updatedAt ?? DateTime.now();

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'bank_name': bankName,
      'account_type': accountType,
      'account_number': accountNumber,
      'iban': iban,
      'balance': balance,
      'currency': currency,
      'card_number': cardNumber,
      'card_holder_name': cardHolderName,
      'expiry_date': expiryDate,
      'cvv': cvv,
      'is_active': isActive ? 1 : 0,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  factory BankAccount.fromMap(Map<String, dynamic> map) {
    return BankAccount(
      id: map['id']?.toInt(),
      bankName: map['bank_name'] ?? '',
      accountType: map['account_type'] ?? '',
      accountNumber: map['account_number'] ?? '',
      iban: map['iban'],
      balance: map['balance']?.toDouble() ?? 0.0,
      currency: map['currency'] ?? 'TRY',
      cardNumber: map['card_number'],
      cardHolderName: map['card_holder_name'],
      expiryDate: map['expiry_date'],
      cvv: map['cvv'],
      isActive: (map['is_active'] ?? 1) == 1,
      createdAt: DateTime.parse(map['created_at']),
      updatedAt: DateTime.parse(map['updated_at']),
    );
  }

  BankAccount copyWith({
    int? id,
    String? bankName,
    String? accountType,
    String? accountNumber,
    String? iban,
    double? balance,
    String? currency,
    String? cardNumber,
    String? cardHolderName,
    String? expiryDate,
    String? cvv,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return BankAccount(
      id: id ?? this.id,
      bankName: bankName ?? this.bankName,
      accountType: accountType ?? this.accountType,
      accountNumber: accountNumber ?? this.accountNumber,
      iban: iban ?? this.iban,
      balance: balance ?? this.balance,
      currency: currency ?? this.currency,
      cardNumber: cardNumber ?? this.cardNumber,
      cardHolderName: cardHolderName ?? this.cardHolderName,
      expiryDate: expiryDate ?? this.expiryDate,
      cvv: cvv ?? this.cvv,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? DateTime.now(),
    );
  }

  @override
  String toString() {
    return 'BankAccount(id: $id, bankName: $bankName, accountType: $accountType, accountNumber: $accountNumber, balance: $balance, currency: $currency)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is BankAccount &&
        other.id == id &&
        other.bankName == bankName &&
        other.accountType == accountType &&
        other.accountNumber == accountNumber &&
        other.iban == iban &&
        other.balance == balance &&
        other.currency == currency &&
        other.cardNumber == cardNumber &&
        other.cardHolderName == cardHolderName &&
        other.expiryDate == expiryDate &&
        other.cvv == cvv &&
        other.isActive == isActive;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        bankName.hashCode ^
        accountType.hashCode ^
        accountNumber.hashCode ^
        iban.hashCode ^
        balance.hashCode ^
        currency.hashCode ^
        cardNumber.hashCode ^
        cardHolderName.hashCode ^
        expiryDate.hashCode ^
        cvv.hashCode ^
        isActive.hashCode;
  }

  // Kart numarasını maskele
  String get maskedCardNumber {
    if (cardNumber == null || cardNumber!.length < 4) return '****';
    return '**** **** **** ${cardNumber!.substring(cardNumber!.length - 4)}';
  }

  // IBAN'ı maskele
  String get maskedIban {
    if (iban == null || iban!.length < 4) return '****';
    return '${iban!.substring(0, 4)}****${iban!.substring(iban!.length - 4)}';
  }

  // Hesap numarasını maskele
  String get maskedAccountNumber {
    if (accountNumber.length < 4) return '****';
    return '****${accountNumber.substring(accountNumber.length - 4)}';
  }

  // Bakiye formatı
  String get formattedBalance {
    return '$currency ${balance.toStringAsFixed(2)}';
  }

  // Hesap türü ikonu
  String get accountTypeIcon {
    switch (accountType.toLowerCase()) {
      case 'vadesiz':
      case 'checking':
        return '💳';
      case 'vadeli':
      case 'savings':
        return '💰';
      case 'kredi':
      case 'credit':
        return '🏦';
      case 'yatırım':
      case 'investment':
        return '📈';
      default:
        return '🏛️';
    }
  }

  // Banka ikonu (basit)
  String get bankIcon {
    switch (bankName.toLowerCase()) {
      case 'ziraat':
      case 'ziraat bankası':
        return '🌾';
      case 'garanti':
      case 'garanti bbva':
        return '🟢';
      case 'işbank':
      case 'iş bankası':
        return '🔵';
      case 'akbank':
        return '🔴';
      case 'yapı kredi':
        return '🟡';
      case 'halkbank':
        return '🟠';
      case 'vakıfbank':
        return '🟣';
      default:
        return '🏦';
    }
  }
}
