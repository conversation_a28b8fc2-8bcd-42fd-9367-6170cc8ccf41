{"buildFiles": ["C:\\DEV\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Desktop\\uygulama\\finansal_yonetim\\build\\.cxx\\Debug\\h5xf356z\\x86", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Desktop\\uygulama\\finansal_yonetim\\build\\.cxx\\Debug\\h5xf356z\\x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}