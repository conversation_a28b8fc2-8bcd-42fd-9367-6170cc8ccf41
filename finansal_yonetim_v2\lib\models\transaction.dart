enum TransactionType {
  income,
  expense,
  transfer,
  investment,
}

enum TransactionCategory {
  // <PERSON><PERSON><PERSON> kategorileri
  salary,
  bonus,
  investment_return,
  freelance,
  business,
  gift,
  other_income,
  
  // Gider kategorileri
  food,
  transport,
  shopping,
  bills,
  entertainment,
  health,
  education,
  travel,
  home,
  insurance,
  tax,
  other_expense,
  
  // Transfer kategorileri
  bank_transfer,
  crypto_transfer,
  
  // Yatırım kategorileri
  crypto_buy,
  crypto_sell,
  stock_buy,
  stock_sell,
}

class TransactionModel {
  final int? id;
  final TransactionType type;
  final TransactionCategory category;
  final double amount;
  final String? description;
  final int? accountId;
  final int? cryptoId;
  final DateTime date;
  final DateTime createdAt;

  TransactionModel({
    this.id,
    required this.type,
    required this.category,
    required this.amount,
    this.description,
    this.accountId,
    this.cryptoId,
    required this.date,
    DateTime? createdAt,
  }) : createdAt = createdAt ?? DateTime.now();

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'type': type.name,
      'category': category.name,
      'amount': amount,
      'description': description,
      'account_id': accountId,
      'crypto_id': cryptoId,
      'date': date.toIso8601String(),
      'created_at': createdAt.toIso8601String(),
    };
  }

  factory TransactionModel.fromMap(Map<String, dynamic> map) {
    return TransactionModel(
      id: map['id']?.toInt(),
      type: TransactionType.values.firstWhere(
        (e) => e.name == map['type'],
        orElse: () => TransactionType.expense,
      ),
      category: TransactionCategory.values.firstWhere(
        (e) => e.name == map['category'],
        orElse: () => TransactionCategory.other_expense,
      ),
      amount: map['amount']?.toDouble() ?? 0.0,
      description: map['description'],
      accountId: map['account_id']?.toInt(),
      cryptoId: map['crypto_id']?.toInt(),
      date: DateTime.parse(map['date']),
      createdAt: DateTime.parse(map['created_at']),
    );
  }

  TransactionModel copyWith({
    int? id,
    TransactionType? type,
    TransactionCategory? category,
    double? amount,
    String? description,
    int? accountId,
    int? cryptoId,
    DateTime? date,
    DateTime? createdAt,
  }) {
    return TransactionModel(
      id: id ?? this.id,
      type: type ?? this.type,
      category: category ?? this.category,
      amount: amount ?? this.amount,
      description: description ?? this.description,
      accountId: accountId ?? this.accountId,
      cryptoId: cryptoId ?? this.cryptoId,
      date: date ?? this.date,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  @override
  String toString() {
    return 'TransactionModel(id: $id, type: $type, category: $category, amount: $amount, date: $date)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is TransactionModel &&
        other.id == id &&
        other.type == type &&
        other.category == category &&
        other.amount == amount &&
        other.description == description &&
        other.accountId == accountId &&
        other.cryptoId == cryptoId &&
        other.date == date;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        type.hashCode ^
        category.hashCode ^
        amount.hashCode ^
        description.hashCode ^
        accountId.hashCode ^
        cryptoId.hashCode ^
        date.hashCode;
  }

  // Formatlanmış tutar
  String get formattedAmount {
    final prefix = type == TransactionType.income ? '+' : '-';
    return '$prefix₺${amount.toStringAsFixed(2)}';
  }

  // İşlem türü rengi
  String get typeColor {
    switch (type) {
      case TransactionType.income:
        return '#10B981'; // Yeşil
      case TransactionType.expense:
        return '#EF4444'; // Kırmızı
      case TransactionType.transfer:
        return '#3B82F6'; // Mavi
      case TransactionType.investment:
        return '#8B5CF6'; // Mor
    }
  }

  // İşlem türü ikonu
  String get typeIcon {
    switch (type) {
      case TransactionType.income:
        return '💰';
      case TransactionType.expense:
        return '💸';
      case TransactionType.transfer:
        return '🔄';
      case TransactionType.investment:
        return '📈';
    }
  }

  // Kategori ikonu
  String get categoryIcon {
    switch (category) {
      // Gelir kategorileri
      case TransactionCategory.salary:
        return '💼';
      case TransactionCategory.bonus:
        return '🎁';
      case TransactionCategory.investment_return:
        return '📊';
      case TransactionCategory.freelance:
        return '💻';
      case TransactionCategory.business:
        return '🏢';
      case TransactionCategory.gift:
        return '🎉';
      case TransactionCategory.other_income:
        return '💰';
      
      // Gider kategorileri
      case TransactionCategory.food:
        return '🍽️';
      case TransactionCategory.transport:
        return '🚗';
      case TransactionCategory.shopping:
        return '🛍️';
      case TransactionCategory.bills:
        return '📄';
      case TransactionCategory.entertainment:
        return '🎬';
      case TransactionCategory.health:
        return '🏥';
      case TransactionCategory.education:
        return '📚';
      case TransactionCategory.travel:
        return '✈️';
      case TransactionCategory.home:
        return '🏠';
      case TransactionCategory.insurance:
        return '🛡️';
      case TransactionCategory.tax:
        return '📋';
      case TransactionCategory.other_expense:
        return '💸';
      
      // Transfer kategorileri
      case TransactionCategory.bank_transfer:
        return '🏦';
      case TransactionCategory.crypto_transfer:
        return '🪙';
      
      // Yatırım kategorileri
      case TransactionCategory.crypto_buy:
        return '📈';
      case TransactionCategory.crypto_sell:
        return '📉';
      case TransactionCategory.stock_buy:
        return '📊';
      case TransactionCategory.stock_sell:
        return '📉';
    }
  }

  // Kategori adı (Türkçe)
  String get categoryName {
    switch (category) {
      // Gelir kategorileri
      case TransactionCategory.salary:
        return 'Maaş';
      case TransactionCategory.bonus:
        return 'Bonus';
      case TransactionCategory.investment_return:
        return 'Yatırım Getirisi';
      case TransactionCategory.freelance:
        return 'Serbest Çalışma';
      case TransactionCategory.business:
        return 'İş';
      case TransactionCategory.gift:
        return 'Hediye';
      case TransactionCategory.other_income:
        return 'Diğer Gelir';
      
      // Gider kategorileri
      case TransactionCategory.food:
        return 'Yemek';
      case TransactionCategory.transport:
        return 'Ulaşım';
      case TransactionCategory.shopping:
        return 'Alışveriş';
      case TransactionCategory.bills:
        return 'Faturalar';
      case TransactionCategory.entertainment:
        return 'Eğlence';
      case TransactionCategory.health:
        return 'Sağlık';
      case TransactionCategory.education:
        return 'Eğitim';
      case TransactionCategory.travel:
        return 'Seyahat';
      case TransactionCategory.home:
        return 'Ev';
      case TransactionCategory.insurance:
        return 'Sigorta';
      case TransactionCategory.tax:
        return 'Vergi';
      case TransactionCategory.other_expense:
        return 'Diğer Gider';
      
      // Transfer kategorileri
      case TransactionCategory.bank_transfer:
        return 'Banka Transferi';
      case TransactionCategory.crypto_transfer:
        return 'Kripto Transferi';
      
      // Yatırım kategorileri
      case TransactionCategory.crypto_buy:
        return 'Kripto Alım';
      case TransactionCategory.crypto_sell:
        return 'Kripto Satım';
      case TransactionCategory.stock_buy:
        return 'Hisse Alım';
      case TransactionCategory.stock_sell:
        return 'Hisse Satım';
    }
  }

  // İşlem türü adı (Türkçe)
  String get typeName {
    switch (type) {
      case TransactionType.income:
        return 'Gelir';
      case TransactionType.expense:
        return 'Gider';
      case TransactionType.transfer:
        return 'Transfer';
      case TransactionType.investment:
        return 'Yatırım';
    }
  }
}
