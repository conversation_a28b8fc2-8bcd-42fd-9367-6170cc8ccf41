int anim abc_fade_in 0x7f010000
int anim abc_fade_out 0x7f010001
int anim abc_grow_fade_in_from_bottom 0x7f010002
int anim abc_popup_enter 0x7f010003
int anim abc_popup_exit 0x7f010004
int anim abc_shrink_fade_out_from_bottom 0x7f010005
int anim abc_slide_in_bottom 0x7f010006
int anim abc_slide_in_top 0x7f010007
int anim abc_slide_out_bottom 0x7f010008
int anim abc_slide_out_top 0x7f010009
int anim abc_tooltip_enter 0x7f01000a
int anim abc_tooltip_exit 0x7f01000b
int anim btn_checkbox_to_checked_box_inner_merged_animation 0x7f01000c
int anim btn_checkbox_to_checked_box_outer_merged_animation 0x7f01000d
int anim btn_checkbox_to_checked_icon_null_animation 0x7f01000e
int anim btn_checkbox_to_unchecked_box_inner_merged_animation 0x7f01000f
int anim btn_checkbox_to_unchecked_check_path_merged_animation 0x7f010010
int anim btn_checkbox_to_unchecked_icon_null_animation 0x7f010011
int anim btn_radio_to_off_mtrl_dot_group_animation 0x7f010012
int anim btn_radio_to_off_mtrl_ring_outer_animation 0x7f010013
int anim btn_radio_to_off_mtrl_ring_outer_path_animation 0x7f010014
int anim btn_radio_to_on_mtrl_dot_group_animation 0x7f010015
int anim btn_radio_to_on_mtrl_ring_outer_animation 0x7f010016
int anim btn_radio_to_on_mtrl_ring_outer_path_animation 0x7f010017
int anim fragment_fast_out_extra_slow_in 0x7f010018
int animator fragment_close_enter 0x7f020000
int animator fragment_close_exit 0x7f020001
int animator fragment_fade_enter 0x7f020002
int animator fragment_fade_exit 0x7f020003
int animator fragment_open_enter 0x7f020004
int animator fragment_open_exit 0x7f020005
int array assume_strong_biometrics_models 0x7f030000
int array crypto_fingerprint_fallback_prefixes 0x7f030001
int array crypto_fingerprint_fallback_vendors 0x7f030002
int array delay_showing_prompt_models 0x7f030003
int array hide_fingerprint_instantly_prefixes 0x7f030004
int attr actionBarDivider 0x7f040000
int attr actionBarItemBackground 0x7f040001
int attr actionBarPopupTheme 0x7f040002
int attr actionBarSize 0x7f040003
int attr actionBarSplitStyle 0x7f040004
int attr actionBarStyle 0x7f040005
int attr actionBarTabBarStyle 0x7f040006
int attr actionBarTabStyle 0x7f040007
int attr actionBarTabTextStyle 0x7f040008
int attr actionBarTheme 0x7f040009
int attr actionBarWidgetTheme 0x7f04000a
int attr actionButtonStyle 0x7f04000b
int attr actionDropDownStyle 0x7f04000c
int attr actionLayout 0x7f04000d
int attr actionMenuTextAppearance 0x7f04000e
int attr actionMenuTextColor 0x7f04000f
int attr actionModeBackground 0x7f040010
int attr actionModeCloseButtonStyle 0x7f040011
int attr actionModeCloseDrawable 0x7f040012
int attr actionModeCopyDrawable 0x7f040013
int attr actionModeCutDrawable 0x7f040014
int attr actionModeFindDrawable 0x7f040015
int attr actionModePasteDrawable 0x7f040016
int attr actionModePopupWindowStyle 0x7f040017
int attr actionModeSelectAllDrawable 0x7f040018
int attr actionModeShareDrawable 0x7f040019
int attr actionModeSplitBackground 0x7f04001a
int attr actionModeStyle 0x7f04001b
int attr actionModeWebSearchDrawable 0x7f04001c
int attr actionOverflowButtonStyle 0x7f04001d
int attr actionOverflowMenuStyle 0x7f04001e
int attr actionProviderClass 0x7f04001f
int attr actionViewClass 0x7f040020
int attr activityAction 0x7f040021
int attr activityChooserViewStyle 0x7f040022
int attr activityName 0x7f040023
int attr adjustable 0x7f040024
int attr alertDialogButtonGroupStyle 0x7f040025
int attr alertDialogCenterButtons 0x7f040026
int attr alertDialogStyle 0x7f040027
int attr alertDialogTheme 0x7f040028
int attr allowDividerAbove 0x7f040029
int attr allowDividerAfterLastItem 0x7f04002a
int attr allowDividerBelow 0x7f04002b
int attr allowStacking 0x7f04002c
int attr alpha 0x7f04002d
int attr alphabeticModifiers 0x7f04002e
int attr alwaysExpand 0x7f04002f
int attr animationBackgroundColor 0x7f040030
int attr arrowHeadLength 0x7f040031
int attr arrowShaftLength 0x7f040032
int attr autoCompleteTextViewStyle 0x7f040033
int attr autoSizeMaxTextSize 0x7f040034
int attr autoSizeMinTextSize 0x7f040035
int attr autoSizePresetSizes 0x7f040036
int attr autoSizeStepGranularity 0x7f040037
int attr autoSizeTextType 0x7f040038
int attr background 0x7f040039
int attr backgroundSplit 0x7f04003a
int attr backgroundStacked 0x7f04003b
int attr backgroundTint 0x7f04003c
int attr backgroundTintMode 0x7f04003d
int attr barLength 0x7f04003e
int attr borderlessButtonStyle 0x7f04003f
int attr buttonBarButtonStyle 0x7f040040
int attr buttonBarNegativeButtonStyle 0x7f040041
int attr buttonBarNeutralButtonStyle 0x7f040042
int attr buttonBarPositiveButtonStyle 0x7f040043
int attr buttonBarStyle 0x7f040044
int attr buttonCompat 0x7f040045
int attr buttonGravity 0x7f040046
int attr buttonIconDimen 0x7f040047
int attr buttonPanelSideLayout 0x7f040048
int attr buttonStyle 0x7f040049
int attr buttonStyleSmall 0x7f04004a
int attr buttonTint 0x7f04004b
int attr buttonTintMode 0x7f04004c
int attr checkBoxPreferenceStyle 0x7f04004d
int attr checkboxStyle 0x7f04004e
int attr checkedTextViewStyle 0x7f04004f
int attr clearTop 0x7f040050
int attr closeIcon 0x7f040051
int attr closeItemLayout 0x7f040052
int attr collapseContentDescription 0x7f040053
int attr collapseIcon 0x7f040054
int attr color 0x7f040055
int attr colorAccent 0x7f040056
int attr colorBackgroundFloating 0x7f040057
int attr colorButtonNormal 0x7f040058
int attr colorControlActivated 0x7f040059
int attr colorControlHighlight 0x7f04005a
int attr colorControlNormal 0x7f04005b
int attr colorError 0x7f04005c
int attr colorPrimary 0x7f04005d
int attr colorPrimaryDark 0x7f04005e
int attr colorSwitchThumbNormal 0x7f04005f
int attr commitIcon 0x7f040060
int attr contentDescription 0x7f040061
int attr contentInsetEnd 0x7f040062
int attr contentInsetEndWithActions 0x7f040063
int attr contentInsetLeft 0x7f040064
int attr contentInsetRight 0x7f040065
int attr contentInsetStart 0x7f040066
int attr contentInsetStartWithNavigation 0x7f040067
int attr controlBackground 0x7f040068
int attr coordinatorLayoutStyle 0x7f040069
int attr customNavigationLayout 0x7f04006a
int attr defaultQueryHint 0x7f04006b
int attr defaultValue 0x7f04006c
int attr dependency 0x7f04006d
int attr dialogCornerRadius 0x7f04006e
int attr dialogIcon 0x7f04006f
int attr dialogLayout 0x7f040070
int attr dialogMessage 0x7f040071
int attr dialogPreferenceStyle 0x7f040072
int attr dialogPreferredPadding 0x7f040073
int attr dialogTheme 0x7f040074
int attr dialogTitle 0x7f040075
int attr disableDependentsState 0x7f040076
int attr displayOptions 0x7f040077
int attr divider 0x7f040078
int attr dividerHorizontal 0x7f040079
int attr dividerPadding 0x7f04007a
int attr dividerVertical 0x7f04007b
int attr drawableBottomCompat 0x7f04007c
int attr drawableEndCompat 0x7f04007d
int attr drawableLeftCompat 0x7f04007e
int attr drawableRightCompat 0x7f04007f
int attr drawableSize 0x7f040080
int attr drawableStartCompat 0x7f040081
int attr drawableTint 0x7f040082
int attr drawableTintMode 0x7f040083
int attr drawableTopCompat 0x7f040084
int attr drawerArrowStyle 0x7f040085
int attr dropDownListViewStyle 0x7f040086
int attr dropdownListPreferredItemHeight 0x7f040087
int attr dropdownPreferenceStyle 0x7f040088
int attr editTextBackground 0x7f040089
int attr editTextColor 0x7f04008a
int attr editTextPreferenceStyle 0x7f04008b
int attr editTextStyle 0x7f04008c
int attr elevation 0x7f04008d
int attr enableCopying 0x7f04008e
int attr enabled 0x7f04008f
int attr entries 0x7f040090
int attr entryValues 0x7f040091
int attr expandActivityOverflowButtonDrawable 0x7f040092
int attr fastScrollEnabled 0x7f040093
int attr fastScrollHorizontalThumbDrawable 0x7f040094
int attr fastScrollHorizontalTrackDrawable 0x7f040095
int attr fastScrollVerticalThumbDrawable 0x7f040096
int attr fastScrollVerticalTrackDrawable 0x7f040097
int attr finishPrimaryWithPlaceholder 0x7f040098
int attr finishPrimaryWithSecondary 0x7f040099
int attr finishSecondaryWithPrimary 0x7f04009a
int attr firstBaselineToTopHeight 0x7f04009b
int attr font 0x7f04009c
int attr fontFamily 0x7f04009d
int attr fontProviderAuthority 0x7f04009e
int attr fontProviderCerts 0x7f04009f
int attr fontProviderFetchStrategy 0x7f0400a0
int attr fontProviderFetchTimeout 0x7f0400a1
int attr fontProviderPackage 0x7f0400a2
int attr fontProviderQuery 0x7f0400a3
int attr fontProviderSystemFontFamily 0x7f0400a4
int attr fontStyle 0x7f0400a5
int attr fontVariationSettings 0x7f0400a6
int attr fontWeight 0x7f0400a7
int attr fragment 0x7f0400a8
int attr gapBetweenBars 0x7f0400a9
int attr goIcon 0x7f0400aa
int attr height 0x7f0400ab
int attr hideOnContentScroll 0x7f0400ac
int attr homeAsUpIndicator 0x7f0400ad
int attr homeLayout 0x7f0400ae
int attr icon 0x7f0400af
int attr iconSpaceReserved 0x7f0400b0
int attr iconTint 0x7f0400b1
int attr iconTintMode 0x7f0400b2
int attr iconifiedByDefault 0x7f0400b3
int attr imageButtonStyle 0x7f0400b4
int attr indeterminateProgressStyle 0x7f0400b5
int attr initialActivityCount 0x7f0400b6
int attr initialExpandedChildrenCount 0x7f0400b7
int attr isLightTheme 0x7f0400b8
int attr isPreferenceVisible 0x7f0400b9
int attr itemPadding 0x7f0400ba
int attr key 0x7f0400bb
int attr keylines 0x7f0400bc
int attr lStar 0x7f0400bd
int attr lastBaselineToBottomHeight 0x7f0400be
int attr layout 0x7f0400bf
int attr layoutManager 0x7f0400c0
int attr layout_anchor 0x7f0400c1
int attr layout_anchorGravity 0x7f0400c2
int attr layout_behavior 0x7f0400c3
int attr layout_dodgeInsetEdges 0x7f0400c4
int attr layout_insetEdge 0x7f0400c5
int attr layout_keyline 0x7f0400c6
int attr lineHeight 0x7f0400c7
int attr listChoiceBackgroundIndicator 0x7f0400c8
int attr listChoiceIndicatorMultipleAnimated 0x7f0400c9
int attr listChoiceIndicatorSingleAnimated 0x7f0400ca
int attr listDividerAlertDialog 0x7f0400cb
int attr listItemLayout 0x7f0400cc
int attr listLayout 0x7f0400cd
int attr listMenuViewStyle 0x7f0400ce
int attr listPopupWindowStyle 0x7f0400cf
int attr listPreferredItemHeight 0x7f0400d0
int attr listPreferredItemHeightLarge 0x7f0400d1
int attr listPreferredItemHeightSmall 0x7f0400d2
int attr listPreferredItemPaddingEnd 0x7f0400d3
int attr listPreferredItemPaddingLeft 0x7f0400d4
int attr listPreferredItemPaddingRight 0x7f0400d5
int attr listPreferredItemPaddingStart 0x7f0400d6
int attr logo 0x7f0400d7
int attr logoDescription 0x7f0400d8
int attr maxButtonHeight 0x7f0400d9
int attr maxHeight 0x7f0400da
int attr maxWidth 0x7f0400db
int attr measureWithLargestChild 0x7f0400dc
int attr menu 0x7f0400dd
int attr min 0x7f0400de
int attr multiChoiceItemLayout 0x7f0400df
int attr navigationContentDescription 0x7f0400e0
int attr navigationIcon 0x7f0400e1
int attr navigationMode 0x7f0400e2
int attr negativeButtonText 0x7f0400e3
int attr nestedScrollViewStyle 0x7f0400e4
int attr numericModifiers 0x7f0400e5
int attr order 0x7f0400e6
int attr orderingFromXml 0x7f0400e7
int attr overlapAnchor 0x7f0400e8
int attr paddingBottomNoButtons 0x7f0400e9
int attr paddingEnd 0x7f0400ea
int attr paddingStart 0x7f0400eb
int attr paddingTopNoTitle 0x7f0400ec
int attr panelBackground 0x7f0400ed
int attr panelMenuListTheme 0x7f0400ee
int attr panelMenuListWidth 0x7f0400ef
int attr persistent 0x7f0400f0
int attr placeholderActivityName 0x7f0400f1
int attr popupMenuStyle 0x7f0400f2
int attr popupTheme 0x7f0400f3
int attr popupWindowStyle 0x7f0400f4
int attr positiveButtonText 0x7f0400f5
int attr preferenceCategoryStyle 0x7f0400f6
int attr preferenceCategoryTitleTextAppearance 0x7f0400f7
int attr preferenceCategoryTitleTextColor 0x7f0400f8
int attr preferenceFragmentCompatStyle 0x7f0400f9
int attr preferenceFragmentListStyle 0x7f0400fa
int attr preferenceFragmentStyle 0x7f0400fb
int attr preferenceInformationStyle 0x7f0400fc
int attr preferenceScreenStyle 0x7f0400fd
int attr preferenceStyle 0x7f0400fe
int attr preferenceTheme 0x7f0400ff
int attr preserveIconSpacing 0x7f040100
int attr primaryActivityName 0x7f040101
int attr progressBarPadding 0x7f040102
int attr progressBarStyle 0x7f040103
int attr queryBackground 0x7f040104
int attr queryHint 0x7f040105
int attr queryPatterns 0x7f040106
int attr radioButtonStyle 0x7f040107
int attr ratingBarStyle 0x7f040108
int attr ratingBarStyleIndicator 0x7f040109
int attr ratingBarStyleSmall 0x7f04010a
int attr reverseLayout 0x7f04010b
int attr searchHintIcon 0x7f04010c
int attr searchIcon 0x7f04010d
int attr searchViewStyle 0x7f04010e
int attr secondaryActivityAction 0x7f04010f
int attr secondaryActivityName 0x7f040110
int attr seekBarIncrement 0x7f040111
int attr seekBarPreferenceStyle 0x7f040112
int attr seekBarStyle 0x7f040113
int attr selectable 0x7f040114
int attr selectableItemBackground 0x7f040115
int attr selectableItemBackgroundBorderless 0x7f040116
int attr shortcutMatchRequired 0x7f040117
int attr shouldDisableView 0x7f040118
int attr showAsAction 0x7f040119
int attr showDividers 0x7f04011a
int attr showSeekBarValue 0x7f04011b
int attr showText 0x7f04011c
int attr showTitle 0x7f04011d
int attr singleChoiceItemLayout 0x7f04011e
int attr singleLineTitle 0x7f04011f
int attr spanCount 0x7f040120
int attr spinBars 0x7f040121
int attr spinnerDropDownItemStyle 0x7f040122
int attr spinnerStyle 0x7f040123
int attr splitLayoutDirection 0x7f040124
int attr splitMaxAspectRatioInLandscape 0x7f040125
int attr splitMaxAspectRatioInPortrait 0x7f040126
int attr splitMinHeightDp 0x7f040127
int attr splitMinSmallestWidthDp 0x7f040128
int attr splitMinWidthDp 0x7f040129
int attr splitRatio 0x7f04012a
int attr splitTrack 0x7f04012b
int attr srcCompat 0x7f04012c
int attr stackFromEnd 0x7f04012d
int attr state_above_anchor 0x7f04012e
int attr statusBarBackground 0x7f04012f
int attr stickyPlaceholder 0x7f040130
int attr subMenuArrow 0x7f040131
int attr submitBackground 0x7f040132
int attr subtitle 0x7f040133
int attr subtitleTextAppearance 0x7f040134
int attr subtitleTextColor 0x7f040135
int attr subtitleTextStyle 0x7f040136
int attr suggestionRowLayout 0x7f040137
int attr summary 0x7f040138
int attr summaryOff 0x7f040139
int attr summaryOn 0x7f04013a
int attr switchMinWidth 0x7f04013b
int attr switchPadding 0x7f04013c
int attr switchPreferenceCompatStyle 0x7f04013d
int attr switchPreferenceStyle 0x7f04013e
int attr switchStyle 0x7f04013f
int attr switchTextAppearance 0x7f040140
int attr switchTextOff 0x7f040141
int attr switchTextOn 0x7f040142
int attr tag 0x7f040143
int attr textAllCaps 0x7f040144
int attr textAppearanceLargePopupMenu 0x7f040145
int attr textAppearanceListItem 0x7f040146
int attr textAppearanceListItemSecondary 0x7f040147
int attr textAppearanceListItemSmall 0x7f040148
int attr textAppearancePopupMenuHeader 0x7f040149
int attr textAppearanceSearchResultSubtitle 0x7f04014a
int attr textAppearanceSearchResultTitle 0x7f04014b
int attr textAppearanceSmallPopupMenu 0x7f04014c
int attr textColorAlertDialogListItem 0x7f04014d
int attr textColorSearchUrl 0x7f04014e
int attr textLocale 0x7f04014f
int attr theme 0x7f040150
int attr thickness 0x7f040151
int attr thumbTextPadding 0x7f040152
int attr thumbTint 0x7f040153
int attr thumbTintMode 0x7f040154
int attr tickMark 0x7f040155
int attr tickMarkTint 0x7f040156
int attr tickMarkTintMode 0x7f040157
int attr tint 0x7f040158
int attr tintMode 0x7f040159
int attr title 0x7f04015a
int attr titleMargin 0x7f04015b
int attr titleMarginBottom 0x7f04015c
int attr titleMarginEnd 0x7f04015d
int attr titleMarginStart 0x7f04015e
int attr titleMarginTop 0x7f04015f
int attr titleMargins 0x7f040160
int attr titleTextAppearance 0x7f040161
int attr titleTextColor 0x7f040162
int attr titleTextStyle 0x7f040163
int attr toolbarNavigationButtonStyle 0x7f040164
int attr toolbarStyle 0x7f040165
int attr tooltipForegroundColor 0x7f040166
int attr tooltipFrameBackground 0x7f040167
int attr tooltipText 0x7f040168
int attr track 0x7f040169
int attr trackTint 0x7f04016a
int attr trackTintMode 0x7f04016b
int attr ttcIndex 0x7f04016c
int attr updatesContinuously 0x7f04016d
int attr useSimpleSummaryProvider 0x7f04016e
int attr viewInflaterClass 0x7f04016f
int attr voiceIcon 0x7f040170
int attr widgetLayout 0x7f040171
int attr windowActionBar 0x7f040172
int attr windowActionBarOverlay 0x7f040173
int attr windowActionModeOverlay 0x7f040174
int attr windowFixedHeightMajor 0x7f040175
int attr windowFixedHeightMinor 0x7f040176
int attr windowFixedWidthMajor 0x7f040177
int attr windowFixedWidthMinor 0x7f040178
int attr windowMinWidthMajor 0x7f040179
int attr windowMinWidthMinor 0x7f04017a
int attr windowNoTitle 0x7f04017b
int bool abc_action_bar_embed_tabs 0x7f050000
int bool abc_allow_stacked_button_bar 0x7f050001
int bool abc_config_actionMenuItemAllCaps 0x7f050002
int bool config_materialPreferenceIconSpaceReserved 0x7f050003
int color abc_background_cache_hint_selector_material_dark 0x7f060000
int color abc_background_cache_hint_selector_material_light 0x7f060001
int color abc_btn_colored_borderless_text_material 0x7f060002
int color abc_btn_colored_text_material 0x7f060003
int color abc_color_highlight_material 0x7f060004
int color abc_decor_view_status_guard 0x7f060005
int color abc_decor_view_status_guard_light 0x7f060006
int color abc_hint_foreground_material_dark 0x7f060007
int color abc_hint_foreground_material_light 0x7f060008
int color abc_primary_text_disable_only_material_dark 0x7f060009
int color abc_primary_text_disable_only_material_light 0x7f06000a
int color abc_primary_text_material_dark 0x7f06000b
int color abc_primary_text_material_light 0x7f06000c
int color abc_search_url_text 0x7f06000d
int color abc_search_url_text_normal 0x7f06000e
int color abc_search_url_text_pressed 0x7f06000f
int color abc_search_url_text_selected 0x7f060010
int color abc_secondary_text_material_dark 0x7f060011
int color abc_secondary_text_material_light 0x7f060012
int color abc_tint_btn_checkable 0x7f060013
int color abc_tint_default 0x7f060014
int color abc_tint_edittext 0x7f060015
int color abc_tint_seek_thumb 0x7f060016
int color abc_tint_spinner 0x7f060017
int color abc_tint_switch_track 0x7f060018
int color accent_material_dark 0x7f060019
int color accent_material_light 0x7f06001a
int color androidx_core_ripple_material_light 0x7f06001b
int color androidx_core_secondary_text_default_material_light 0x7f06001c
int color background_floating_material_dark 0x7f06001d
int color background_floating_material_light 0x7f06001e
int color background_material_dark 0x7f06001f
int color background_material_light 0x7f060020
int color biometric_error_color 0x7f060021
int color black_text 0x7f060022
int color bright_foreground_disabled_material_dark 0x7f060023
int color bright_foreground_disabled_material_light 0x7f060024
int color bright_foreground_inverse_material_dark 0x7f060025
int color bright_foreground_inverse_material_light 0x7f060026
int color bright_foreground_material_dark 0x7f060027
int color bright_foreground_material_light 0x7f060028
int color button_material_dark 0x7f060029
int color button_material_light 0x7f06002a
int color call_notification_answer_color 0x7f06002b
int color call_notification_decline_color 0x7f06002c
int color dim_foreground_disabled_material_dark 0x7f06002d
int color dim_foreground_disabled_material_light 0x7f06002e
int color dim_foreground_material_dark 0x7f06002f
int color dim_foreground_material_light 0x7f060030
int color error_color_material_dark 0x7f060031
int color error_color_material_light 0x7f060032
int color foreground_material_dark 0x7f060033
int color foreground_material_light 0x7f060034
int color grey_text 0x7f060035
int color highlighted_text_material_dark 0x7f060036
int color highlighted_text_material_light 0x7f060037
int color material_blue_grey_800 0x7f060038
int color material_blue_grey_900 0x7f060039
int color material_blue_grey_950 0x7f06003a
int color material_deep_teal_200 0x7f06003b
int color material_deep_teal_500 0x7f06003c
int color material_grey_100 0x7f06003d
int color material_grey_300 0x7f06003e
int color material_grey_50 0x7f06003f
int color material_grey_600 0x7f060040
int color material_grey_800 0x7f060041
int color material_grey_850 0x7f060042
int color material_grey_900 0x7f060043
int color notification_action_color_filter 0x7f060044
int color notification_icon_bg_color 0x7f060045
int color preference_fallback_accent_color 0x7f060046
int color primary_dark_material_dark 0x7f060047
int color primary_dark_material_light 0x7f060048
int color primary_material_dark 0x7f060049
int color primary_material_light 0x7f06004a
int color primary_text_default_material_dark 0x7f06004b
int color primary_text_default_material_light 0x7f06004c
int color primary_text_disabled_material_dark 0x7f06004d
int color primary_text_disabled_material_light 0x7f06004e
int color ripple_material_dark 0x7f06004f
int color ripple_material_light 0x7f060050
int color secondary_text_default_material_dark 0x7f060051
int color secondary_text_default_material_light 0x7f060052
int color secondary_text_disabled_material_dark 0x7f060053
int color secondary_text_disabled_material_light 0x7f060054
int color switch_thumb_disabled_material_dark 0x7f060055
int color switch_thumb_disabled_material_light 0x7f060056
int color switch_thumb_material_dark 0x7f060057
int color switch_thumb_material_light 0x7f060058
int color switch_thumb_normal_material_dark 0x7f060059
int color switch_thumb_normal_material_light 0x7f06005a
int color tooltip_background_dark 0x7f06005b
int color tooltip_background_light 0x7f06005c
int dimen abc_action_bar_content_inset_material 0x7f070000
int dimen abc_action_bar_content_inset_with_nav 0x7f070001
int dimen abc_action_bar_default_height_material 0x7f070002
int dimen abc_action_bar_default_padding_end_material 0x7f070003
int dimen abc_action_bar_default_padding_start_material 0x7f070004
int dimen abc_action_bar_elevation_material 0x7f070005
int dimen abc_action_bar_icon_vertical_padding_material 0x7f070006
int dimen abc_action_bar_overflow_padding_end_material 0x7f070007
int dimen abc_action_bar_overflow_padding_start_material 0x7f070008
int dimen abc_action_bar_stacked_max_height 0x7f070009
int dimen abc_action_bar_stacked_tab_max_width 0x7f07000a
int dimen abc_action_bar_subtitle_bottom_margin_material 0x7f07000b
int dimen abc_action_bar_subtitle_top_margin_material 0x7f07000c
int dimen abc_action_button_min_height_material 0x7f07000d
int dimen abc_action_button_min_width_material 0x7f07000e
int dimen abc_action_button_min_width_overflow_material 0x7f07000f
int dimen abc_alert_dialog_button_bar_height 0x7f070010
int dimen abc_alert_dialog_button_dimen 0x7f070011
int dimen abc_button_inset_horizontal_material 0x7f070012
int dimen abc_button_inset_vertical_material 0x7f070013
int dimen abc_button_padding_horizontal_material 0x7f070014
int dimen abc_button_padding_vertical_material 0x7f070015
int dimen abc_cascading_menus_min_smallest_width 0x7f070016
int dimen abc_config_prefDialogWidth 0x7f070017
int dimen abc_control_corner_material 0x7f070018
int dimen abc_control_inset_material 0x7f070019
int dimen abc_control_padding_material 0x7f07001a
int dimen abc_dialog_corner_radius_material 0x7f07001b
int dimen abc_dialog_fixed_height_major 0x7f07001c
int dimen abc_dialog_fixed_height_minor 0x7f07001d
int dimen abc_dialog_fixed_width_major 0x7f07001e
int dimen abc_dialog_fixed_width_minor 0x7f07001f
int dimen abc_dialog_list_padding_bottom_no_buttons 0x7f070020
int dimen abc_dialog_list_padding_top_no_title 0x7f070021
int dimen abc_dialog_min_width_major 0x7f070022
int dimen abc_dialog_min_width_minor 0x7f070023
int dimen abc_dialog_padding_material 0x7f070024
int dimen abc_dialog_padding_top_material 0x7f070025
int dimen abc_dialog_title_divider_material 0x7f070026
int dimen abc_disabled_alpha_material_dark 0x7f070027
int dimen abc_disabled_alpha_material_light 0x7f070028
int dimen abc_dropdownitem_icon_width 0x7f070029
int dimen abc_dropdownitem_text_padding_left 0x7f07002a
int dimen abc_dropdownitem_text_padding_right 0x7f07002b
int dimen abc_edit_text_inset_bottom_material 0x7f07002c
int dimen abc_edit_text_inset_horizontal_material 0x7f07002d
int dimen abc_edit_text_inset_top_material 0x7f07002e
int dimen abc_floating_window_z 0x7f07002f
int dimen abc_list_item_height_large_material 0x7f070030
int dimen abc_list_item_height_material 0x7f070031
int dimen abc_list_item_height_small_material 0x7f070032
int dimen abc_list_item_padding_horizontal_material 0x7f070033
int dimen abc_panel_menu_list_width 0x7f070034
int dimen abc_progress_bar_height_material 0x7f070035
int dimen abc_search_view_preferred_height 0x7f070036
int dimen abc_search_view_preferred_width 0x7f070037
int dimen abc_seekbar_track_background_height_material 0x7f070038
int dimen abc_seekbar_track_progress_height_material 0x7f070039
int dimen abc_select_dialog_padding_start_material 0x7f07003a
int dimen abc_switch_padding 0x7f07003b
int dimen abc_text_size_body_1_material 0x7f07003c
int dimen abc_text_size_body_2_material 0x7f07003d
int dimen abc_text_size_button_material 0x7f07003e
int dimen abc_text_size_caption_material 0x7f07003f
int dimen abc_text_size_display_1_material 0x7f070040
int dimen abc_text_size_display_2_material 0x7f070041
int dimen abc_text_size_display_3_material 0x7f070042
int dimen abc_text_size_display_4_material 0x7f070043
int dimen abc_text_size_headline_material 0x7f070044
int dimen abc_text_size_large_material 0x7f070045
int dimen abc_text_size_medium_material 0x7f070046
int dimen abc_text_size_menu_header_material 0x7f070047
int dimen abc_text_size_menu_material 0x7f070048
int dimen abc_text_size_small_material 0x7f070049
int dimen abc_text_size_subhead_material 0x7f07004a
int dimen abc_text_size_subtitle_material_toolbar 0x7f07004b
int dimen abc_text_size_title_material 0x7f07004c
int dimen abc_text_size_title_material_toolbar 0x7f07004d
int dimen compat_button_inset_horizontal_material 0x7f07004e
int dimen compat_button_inset_vertical_material 0x7f07004f
int dimen compat_button_padding_horizontal_material 0x7f070050
int dimen compat_button_padding_vertical_material 0x7f070051
int dimen compat_control_corner_material 0x7f070052
int dimen compat_notification_large_icon_max_height 0x7f070053
int dimen compat_notification_large_icon_max_width 0x7f070054
int dimen disabled_alpha_material_dark 0x7f070055
int dimen disabled_alpha_material_light 0x7f070056
int dimen fastscroll_default_thickness 0x7f070057
int dimen fastscroll_margin 0x7f070058
int dimen fastscroll_minimum_range 0x7f070059
int dimen fingerprint_icon_size 0x7f07005a
int dimen highlight_alpha_material_colored 0x7f07005b
int dimen highlight_alpha_material_dark 0x7f07005c
int dimen highlight_alpha_material_light 0x7f07005d
int dimen hint_alpha_material_dark 0x7f07005e
int dimen hint_alpha_material_light 0x7f07005f
int dimen hint_pressed_alpha_material_dark 0x7f070060
int dimen hint_pressed_alpha_material_light 0x7f070061
int dimen huge_text_size 0x7f070062
int dimen item_touch_helper_max_drag_scroll_per_frame 0x7f070063
int dimen item_touch_helper_swipe_escape_max_velocity 0x7f070064
int dimen item_touch_helper_swipe_escape_velocity 0x7f070065
int dimen medium_text_size 0x7f070066
int dimen notification_action_icon_size 0x7f070067
int dimen notification_action_text_size 0x7f070068
int dimen notification_big_circle_margin 0x7f070069
int dimen notification_content_margin_start 0x7f07006a
int dimen notification_large_icon_height 0x7f07006b
int dimen notification_large_icon_width 0x7f07006c
int dimen notification_main_column_padding_top 0x7f07006d
int dimen notification_media_narrow_margin 0x7f07006e
int dimen notification_right_icon_size 0x7f07006f
int dimen notification_right_side_padding_top 0x7f070070
int dimen notification_small_icon_background_padding 0x7f070071
int dimen notification_small_icon_size_as_large 0x7f070072
int dimen notification_subtext_size 0x7f070073
int dimen notification_top_pad 0x7f070074
int dimen notification_top_pad_large_text 0x7f070075
int dimen preference_dropdown_padding_start 0x7f070076
int dimen preference_icon_minWidth 0x7f070077
int dimen preference_seekbar_padding_horizontal 0x7f070078
int dimen preference_seekbar_padding_vertical 0x7f070079
int dimen preference_seekbar_value_minWidth 0x7f07007a
int dimen preferences_detail_width 0x7f07007b
int dimen preferences_header_width 0x7f07007c
int dimen tooltip_corner_radius 0x7f07007d
int dimen tooltip_horizontal_padding 0x7f07007e
int dimen tooltip_margin 0x7f07007f
int dimen tooltip_precise_anchor_extra_offset 0x7f070080
int dimen tooltip_precise_anchor_threshold 0x7f070081
int dimen tooltip_vertical_padding 0x7f070082
int dimen tooltip_y_offset_non_touch 0x7f070083
int dimen tooltip_y_offset_touch 0x7f070084
int drawable abc_ab_share_pack_mtrl_alpha 0x7f080000
int drawable abc_action_bar_item_background_material 0x7f080001
int drawable abc_btn_borderless_material 0x7f080002
int drawable abc_btn_check_material 0x7f080003
int drawable abc_btn_check_material_anim 0x7f080004
int drawable abc_btn_check_to_on_mtrl_000 0x7f080005
int drawable abc_btn_check_to_on_mtrl_015 0x7f080006
int drawable abc_btn_colored_material 0x7f080007
int drawable abc_btn_default_mtrl_shape 0x7f080008
int drawable abc_btn_radio_material 0x7f080009
int drawable abc_btn_radio_material_anim 0x7f08000a
int drawable abc_btn_radio_to_on_mtrl_000 0x7f08000b
int drawable abc_btn_radio_to_on_mtrl_015 0x7f08000c
int drawable abc_btn_switch_to_on_mtrl_00001 0x7f08000d
int drawable abc_btn_switch_to_on_mtrl_00012 0x7f08000e
int drawable abc_cab_background_internal_bg 0x7f08000f
int drawable abc_cab_background_top_material 0x7f080010
int drawable abc_cab_background_top_mtrl_alpha 0x7f080011
int drawable abc_control_background_material 0x7f080012
int drawable abc_dialog_material_background 0x7f080013
int drawable abc_edit_text_material 0x7f080014
int drawable abc_ic_ab_back_material 0x7f080015
int drawable abc_ic_arrow_drop_right_black_24dp 0x7f080016
int drawable abc_ic_clear_material 0x7f080017
int drawable abc_ic_commit_search_api_mtrl_alpha 0x7f080018
int drawable abc_ic_go_search_api_material 0x7f080019
int drawable abc_ic_menu_copy_mtrl_am_alpha 0x7f08001a
int drawable abc_ic_menu_cut_mtrl_alpha 0x7f08001b
int drawable abc_ic_menu_overflow_material 0x7f08001c
int drawable abc_ic_menu_paste_mtrl_am_alpha 0x7f08001d
int drawable abc_ic_menu_selectall_mtrl_alpha 0x7f08001e
int drawable abc_ic_menu_share_mtrl_alpha 0x7f08001f
int drawable abc_ic_search_api_material 0x7f080020
int drawable abc_ic_star_black_16dp 0x7f080021
int drawable abc_ic_star_black_36dp 0x7f080022
int drawable abc_ic_star_black_48dp 0x7f080023
int drawable abc_ic_star_half_black_16dp 0x7f080024
int drawable abc_ic_star_half_black_36dp 0x7f080025
int drawable abc_ic_star_half_black_48dp 0x7f080026
int drawable abc_ic_voice_search_api_material 0x7f080027
int drawable abc_item_background_holo_dark 0x7f080028
int drawable abc_item_background_holo_light 0x7f080029
int drawable abc_list_divider_material 0x7f08002a
int drawable abc_list_divider_mtrl_alpha 0x7f08002b
int drawable abc_list_focused_holo 0x7f08002c
int drawable abc_list_longpressed_holo 0x7f08002d
int drawable abc_list_pressed_holo_dark 0x7f08002e
int drawable abc_list_pressed_holo_light 0x7f08002f
int drawable abc_list_selector_background_transition_holo_dark 0x7f080030
int drawable abc_list_selector_background_transition_holo_light 0x7f080031
int drawable abc_list_selector_disabled_holo_dark 0x7f080032
int drawable abc_list_selector_disabled_holo_light 0x7f080033
int drawable abc_list_selector_holo_dark 0x7f080034
int drawable abc_list_selector_holo_light 0x7f080035
int drawable abc_menu_hardkey_panel_mtrl_mult 0x7f080036
int drawable abc_popup_background_mtrl_mult 0x7f080037
int drawable abc_ratingbar_indicator_material 0x7f080038
int drawable abc_ratingbar_material 0x7f080039
int drawable abc_ratingbar_small_material 0x7f08003a
int drawable abc_scrubber_control_off_mtrl_alpha 0x7f08003b
int drawable abc_scrubber_control_to_pressed_mtrl_000 0x7f08003c
int drawable abc_scrubber_control_to_pressed_mtrl_005 0x7f08003d
int drawable abc_scrubber_primary_mtrl_alpha 0x7f08003e
int drawable abc_scrubber_track_mtrl_alpha 0x7f08003f
int drawable abc_seekbar_thumb_material 0x7f080040
int drawable abc_seekbar_tick_mark_material 0x7f080041
int drawable abc_seekbar_track_material 0x7f080042
int drawable abc_spinner_mtrl_am_alpha 0x7f080043
int drawable abc_spinner_textfield_background_material 0x7f080044
int drawable abc_switch_thumb_material 0x7f080045
int drawable abc_switch_track_mtrl_alpha 0x7f080046
int drawable abc_tab_indicator_material 0x7f080047
int drawable abc_tab_indicator_mtrl_alpha 0x7f080048
int drawable abc_text_cursor_material 0x7f080049
int drawable abc_text_select_handle_left_mtrl_dark 0x7f08004a
int drawable abc_text_select_handle_left_mtrl_light 0x7f08004b
int drawable abc_text_select_handle_middle_mtrl_dark 0x7f08004c
int drawable abc_text_select_handle_middle_mtrl_light 0x7f08004d
int drawable abc_text_select_handle_right_mtrl_dark 0x7f08004e
int drawable abc_text_select_handle_right_mtrl_light 0x7f08004f
int drawable abc_textfield_activated_mtrl_alpha 0x7f080050
int drawable abc_textfield_default_mtrl_alpha 0x7f080051
int drawable abc_textfield_search_activated_mtrl_alpha 0x7f080052
int drawable abc_textfield_search_default_mtrl_alpha 0x7f080053
int drawable abc_textfield_search_material 0x7f080054
int drawable abc_vector_test 0x7f080055
int drawable btn_checkbox_checked_mtrl 0x7f080056
int drawable btn_checkbox_checked_to_unchecked_mtrl_animation 0x7f080057
int drawable btn_checkbox_unchecked_mtrl 0x7f080058
int drawable btn_checkbox_unchecked_to_checked_mtrl_animation 0x7f080059
int drawable btn_radio_off_mtrl 0x7f08005a
int drawable btn_radio_off_to_on_mtrl_animation 0x7f08005b
int drawable btn_radio_on_mtrl 0x7f08005c
int drawable btn_radio_on_to_off_mtrl_animation 0x7f08005d
int drawable fingerprint_dialog_error 0x7f08005e
int drawable fingerprint_dialog_fp_icon 0x7f08005f
int drawable ic_arrow_down_24dp 0x7f080060
int drawable ic_call_answer 0x7f080061
int drawable ic_call_answer_low 0x7f080062
int drawable ic_call_answer_video 0x7f080063
int drawable ic_call_answer_video_low 0x7f080064
int drawable ic_call_decline 0x7f080065
int drawable ic_call_decline_low 0x7f080066
int drawable launch_background 0x7f080067
int drawable notification_action_background 0x7f080068
int drawable notification_bg 0x7f080069
int drawable notification_bg_low 0x7f08006a
int drawable notification_bg_low_normal 0x7f08006b
int drawable notification_bg_low_pressed 0x7f08006c
int drawable notification_bg_normal 0x7f08006d
int drawable notification_bg_normal_pressed 0x7f08006e
int drawable notification_icon_background 0x7f08006f
int drawable notification_oversize_large_icon_bg 0x7f080070
int drawable notification_template_icon_bg 0x7f080071
int drawable notification_template_icon_low_bg 0x7f080072
int drawable notification_tile_bg 0x7f080073
int drawable notify_panel_notification_icon_bg 0x7f080074
int drawable preference_list_divider_material 0x7f080075
int drawable tooltip_frame_dark 0x7f080076
int drawable tooltip_frame_light 0x7f080077
int id ALT 0x7f090000
int id CTRL 0x7f090001
int id FUNCTION 0x7f090002
int id META 0x7f090003
int id SHIFT 0x7f090004
int id SYM 0x7f090005
int id accessibility_action_clickable_span 0x7f090006
int id accessibility_custom_action_0 0x7f090007
int id accessibility_custom_action_1 0x7f090008
int id accessibility_custom_action_10 0x7f090009
int id accessibility_custom_action_11 0x7f09000a
int id accessibility_custom_action_12 0x7f09000b
int id accessibility_custom_action_13 0x7f09000c
int id accessibility_custom_action_14 0x7f09000d
int id accessibility_custom_action_15 0x7f09000e
int id accessibility_custom_action_16 0x7f09000f
int id accessibility_custom_action_17 0x7f090010
int id accessibility_custom_action_18 0x7f090011
int id accessibility_custom_action_19 0x7f090012
int id accessibility_custom_action_2 0x7f090013
int id accessibility_custom_action_20 0x7f090014
int id accessibility_custom_action_21 0x7f090015
int id accessibility_custom_action_22 0x7f090016
int id accessibility_custom_action_23 0x7f090017
int id accessibility_custom_action_24 0x7f090018
int id accessibility_custom_action_25 0x7f090019
int id accessibility_custom_action_26 0x7f09001a
int id accessibility_custom_action_27 0x7f09001b
int id accessibility_custom_action_28 0x7f09001c
int id accessibility_custom_action_29 0x7f09001d
int id accessibility_custom_action_3 0x7f09001e
int id accessibility_custom_action_30 0x7f09001f
int id accessibility_custom_action_31 0x7f090020
int id accessibility_custom_action_4 0x7f090021
int id accessibility_custom_action_5 0x7f090022
int id accessibility_custom_action_6 0x7f090023
int id accessibility_custom_action_7 0x7f090024
int id accessibility_custom_action_8 0x7f090025
int id accessibility_custom_action_9 0x7f090026
int id action_bar 0x7f090027
int id action_bar_activity_content 0x7f090028
int id action_bar_container 0x7f090029
int id action_bar_root 0x7f09002a
int id action_bar_spinner 0x7f09002b
int id action_bar_subtitle 0x7f09002c
int id action_bar_title 0x7f09002d
int id action_container 0x7f09002e
int id action_context_bar 0x7f09002f
int id action_divider 0x7f090030
int id action_image 0x7f090031
int id action_menu_divider 0x7f090032
int id action_menu_presenter 0x7f090033
int id action_mode_bar 0x7f090034
int id action_mode_bar_stub 0x7f090035
int id action_mode_close_button 0x7f090036
int id action_text 0x7f090037
int id actions 0x7f090038
int id activity_chooser_view_content 0x7f090039
int id add 0x7f09003a
int id adjacent 0x7f09003b
int id alertTitle 0x7f09003c
int id all 0x7f09003d
int id always 0x7f09003e
int id alwaysAllow 0x7f09003f
int id alwaysDisallow 0x7f090040
int id androidx_window_activity_scope 0x7f090041
int id async 0x7f090042
int id beginning 0x7f090043
int id blocking 0x7f090044
int id bottom 0x7f090045
int id bottomToTop 0x7f090046
int id buttonPanel 0x7f090047
int id center 0x7f090048
int id center_horizontal 0x7f090049
int id center_vertical 0x7f09004a
int id checkbox 0x7f09004b
int id checked 0x7f09004c
int id chronometer 0x7f09004d
int id clip_horizontal 0x7f09004e
int id clip_vertical 0x7f09004f
int id collapseActionView 0x7f090050
int id content 0x7f090051
int id contentPanel 0x7f090052
int id custom 0x7f090053
int id customPanel 0x7f090054
int id decor_content_parent 0x7f090055
int id default_activity_button 0x7f090056
int id dialog_button 0x7f090057
int id disableHome 0x7f090058
int id edit_query 0x7f090059
int id edit_text_id 0x7f09005a
int id end 0x7f09005b
int id expand_activities_button 0x7f09005c
int id expanded_menu 0x7f09005d
int id fill 0x7f09005e
int id fill_horizontal 0x7f09005f
int id fill_vertical 0x7f090060
int id fingerprint_description 0x7f090061
int id fingerprint_error 0x7f090062
int id fingerprint_icon 0x7f090063
int id fingerprint_required 0x7f090064
int id fingerprint_subtitle 0x7f090065
int id forever 0x7f090066
int id fragment_container_view_tag 0x7f090067
int id ghost_view 0x7f090068
int id ghost_view_holder 0x7f090069
int id go_to_setting_description 0x7f09006a
int id group_divider 0x7f09006b
int id hide_ime_id 0x7f09006c
int id home 0x7f09006d
int id homeAsUp 0x7f09006e
int id icon 0x7f09006f
int id icon_frame 0x7f090070
int id icon_group 0x7f090071
int id ifRoom 0x7f090072
int id image 0x7f090073
int id info 0x7f090074
int id italic 0x7f090075
int id item_touch_helper_previous_elevation 0x7f090076
int id left 0x7f090077
int id line1 0x7f090078
int id line3 0x7f090079
int id listMode 0x7f09007a
int id list_item 0x7f09007b
int id locale 0x7f09007c
int id ltr 0x7f09007d
int id message 0x7f09007e
int id middle 0x7f09007f
int id multiply 0x7f090080
int id never 0x7f090081
int id none 0x7f090082
int id normal 0x7f090083
int id notification_background 0x7f090084
int id notification_main_column 0x7f090085
int id notification_main_column_container 0x7f090086
int id off 0x7f090087
int id on 0x7f090088
int id parentPanel 0x7f090089
int id parent_matrix 0x7f09008a
int id preferences_detail 0x7f09008b
int id preferences_header 0x7f09008c
int id preferences_sliding_pane_layout 0x7f09008d
int id progress_circular 0x7f09008e
int id progress_horizontal 0x7f09008f
int id radio 0x7f090090
int id recycler_view 0x7f090091
int id report_drawn 0x7f090092
int id right 0x7f090093
int id right_icon 0x7f090094
int id right_side 0x7f090095
int id rtl 0x7f090096
int id save_non_transition_alpha 0x7f090097
int id save_overlay_view 0x7f090098
int id screen 0x7f090099
int id scrollIndicatorDown 0x7f09009a
int id scrollIndicatorUp 0x7f09009b
int id scrollView 0x7f09009c
int id search_badge 0x7f09009d
int id search_bar 0x7f09009e
int id search_button 0x7f09009f
int id search_close_btn 0x7f0900a0
int id search_edit_frame 0x7f0900a1
int id search_go_btn 0x7f0900a2
int id search_mag_icon 0x7f0900a3
int id search_plate 0x7f0900a4
int id search_src_text 0x7f0900a5
int id search_voice_btn 0x7f0900a6
int id seekbar 0x7f0900a7
int id seekbar_value 0x7f0900a8
int id select_dialog_listview 0x7f0900a9
int id shortcut 0x7f0900aa
int id showCustom 0x7f0900ab
int id showHome 0x7f0900ac
int id showTitle 0x7f0900ad
int id spacer 0x7f0900ae
int id special_effects_controller_view_tag 0x7f0900af
int id spinner 0x7f0900b0
int id split_action_bar 0x7f0900b1
int id src_atop 0x7f0900b2
int id src_in 0x7f0900b3
int id src_over 0x7f0900b4
int id start 0x7f0900b5
int id submenuarrow 0x7f0900b6
int id submit_area 0x7f0900b7
int id switchWidget 0x7f0900b8
int id tabMode 0x7f0900b9
int id tag_accessibility_actions 0x7f0900ba
int id tag_accessibility_clickable_spans 0x7f0900bb
int id tag_accessibility_heading 0x7f0900bc
int id tag_accessibility_pane_title 0x7f0900bd
int id tag_on_apply_window_listener 0x7f0900be
int id tag_on_receive_content_listener 0x7f0900bf
int id tag_on_receive_content_mime_types 0x7f0900c0
int id tag_screen_reader_focusable 0x7f0900c1
int id tag_state_description 0x7f0900c2
int id tag_transition_group 0x7f0900c3
int id tag_unhandled_key_event_manager 0x7f0900c4
int id tag_unhandled_key_listeners 0x7f0900c5
int id tag_window_insets_animation_callback 0x7f0900c6
int id text 0x7f0900c7
int id text2 0x7f0900c8
int id textSpacerNoButtons 0x7f0900c9
int id textSpacerNoTitle 0x7f0900ca
int id time 0x7f0900cb
int id title 0x7f0900cc
int id titleDividerNoCustom 0x7f0900cd
int id title_template 0x7f0900ce
int id top 0x7f0900cf
int id topPanel 0x7f0900d0
int id topToBottom 0x7f0900d1
int id transition_current_scene 0x7f0900d2
int id transition_layout_save 0x7f0900d3
int id transition_position 0x7f0900d4
int id transition_scene_layoutid_cache 0x7f0900d5
int id transition_transform 0x7f0900d6
int id unchecked 0x7f0900d7
int id uniform 0x7f0900d8
int id up 0x7f0900d9
int id useLogo 0x7f0900da
int id view_tree_lifecycle_owner 0x7f0900db
int id view_tree_on_back_pressed_dispatcher_owner 0x7f0900dc
int id view_tree_saved_state_registry_owner 0x7f0900dd
int id view_tree_view_model_store_owner 0x7f0900de
int id visible_removing_fragment_view_tag 0x7f0900df
int id withText 0x7f0900e0
int id wrap_content 0x7f0900e1
int integer abc_config_activityDefaultDur 0x7f0a0000
int integer abc_config_activityShortDur 0x7f0a0001
int integer cancel_button_image_alpha 0x7f0a0002
int integer config_tooltipAnimTime 0x7f0a0003
int integer preferences_detail_pane_weight 0x7f0a0004
int integer preferences_header_pane_weight 0x7f0a0005
int integer status_bar_notification_info_maxnum 0x7f0a0006
int interpolator btn_checkbox_checked_mtrl_animation_interpolator_0 0x7f0b0000
int interpolator btn_checkbox_checked_mtrl_animation_interpolator_1 0x7f0b0001
int interpolator btn_checkbox_unchecked_mtrl_animation_interpolator_0 0x7f0b0002
int interpolator btn_checkbox_unchecked_mtrl_animation_interpolator_1 0x7f0b0003
int interpolator btn_radio_to_off_mtrl_animation_interpolator_0 0x7f0b0004
int interpolator btn_radio_to_on_mtrl_animation_interpolator_0 0x7f0b0005
int interpolator fast_out_slow_in 0x7f0b0006
int layout abc_action_bar_title_item 0x7f0c0000
int layout abc_action_bar_up_container 0x7f0c0001
int layout abc_action_menu_item_layout 0x7f0c0002
int layout abc_action_menu_layout 0x7f0c0003
int layout abc_action_mode_bar 0x7f0c0004
int layout abc_action_mode_close_item_material 0x7f0c0005
int layout abc_activity_chooser_view 0x7f0c0006
int layout abc_activity_chooser_view_list_item 0x7f0c0007
int layout abc_alert_dialog_button_bar_material 0x7f0c0008
int layout abc_alert_dialog_material 0x7f0c0009
int layout abc_alert_dialog_title_material 0x7f0c000a
int layout abc_cascading_menu_item_layout 0x7f0c000b
int layout abc_dialog_title_material 0x7f0c000c
int layout abc_expanded_menu_layout 0x7f0c000d
int layout abc_list_menu_item_checkbox 0x7f0c000e
int layout abc_list_menu_item_icon 0x7f0c000f
int layout abc_list_menu_item_layout 0x7f0c0010
int layout abc_list_menu_item_radio 0x7f0c0011
int layout abc_popup_menu_header_item_layout 0x7f0c0012
int layout abc_popup_menu_item_layout 0x7f0c0013
int layout abc_screen_content_include 0x7f0c0014
int layout abc_screen_simple 0x7f0c0015
int layout abc_screen_simple_overlay_action_mode 0x7f0c0016
int layout abc_screen_toolbar 0x7f0c0017
int layout abc_search_dropdown_item_icons_2line 0x7f0c0018
int layout abc_search_view 0x7f0c0019
int layout abc_select_dialog_material 0x7f0c001a
int layout abc_tooltip 0x7f0c001b
int layout custom_dialog 0x7f0c001c
int layout expand_button 0x7f0c001d
int layout fingerprint_dialog_layout 0x7f0c001e
int layout go_to_setting 0x7f0c001f
int layout image_frame 0x7f0c0020
int layout ime_base_split_test_activity 0x7f0c0021
int layout ime_secondary_split_test_activity 0x7f0c0022
int layout notification_action 0x7f0c0023
int layout notification_action_tombstone 0x7f0c0024
int layout notification_template_custom_big 0x7f0c0025
int layout notification_template_icon_group 0x7f0c0026
int layout notification_template_part_chronometer 0x7f0c0027
int layout notification_template_part_time 0x7f0c0028
int layout preference 0x7f0c0029
int layout preference_category 0x7f0c002a
int layout preference_category_material 0x7f0c002b
int layout preference_dialog_edittext 0x7f0c002c
int layout preference_dropdown 0x7f0c002d
int layout preference_dropdown_material 0x7f0c002e
int layout preference_information 0x7f0c002f
int layout preference_information_material 0x7f0c0030
int layout preference_list_fragment 0x7f0c0031
int layout preference_material 0x7f0c0032
int layout preference_recyclerview 0x7f0c0033
int layout preference_widget_checkbox 0x7f0c0034
int layout preference_widget_seekbar 0x7f0c0035
int layout preference_widget_seekbar_material 0x7f0c0036
int layout preference_widget_switch 0x7f0c0037
int layout preference_widget_switch_compat 0x7f0c0038
int layout select_dialog_item_material 0x7f0c0039
int layout select_dialog_multichoice_material 0x7f0c003a
int layout select_dialog_singlechoice_material 0x7f0c003b
int layout support_simple_spinner_dropdown_item 0x7f0c003c
int mipmap ic_launcher 0x7f0d0000
int string abc_action_bar_home_description 0x7f0e0000
int string abc_action_bar_up_description 0x7f0e0001
int string abc_action_menu_overflow_description 0x7f0e0002
int string abc_action_mode_done 0x7f0e0003
int string abc_activity_chooser_view_see_all 0x7f0e0004
int string abc_activitychooserview_choose_application 0x7f0e0005
int string abc_capital_off 0x7f0e0006
int string abc_capital_on 0x7f0e0007
int string abc_menu_alt_shortcut_label 0x7f0e0008
int string abc_menu_ctrl_shortcut_label 0x7f0e0009
int string abc_menu_delete_shortcut_label 0x7f0e000a
int string abc_menu_enter_shortcut_label 0x7f0e000b
int string abc_menu_function_shortcut_label 0x7f0e000c
int string abc_menu_meta_shortcut_label 0x7f0e000d
int string abc_menu_shift_shortcut_label 0x7f0e000e
int string abc_menu_space_shortcut_label 0x7f0e000f
int string abc_menu_sym_shortcut_label 0x7f0e0010
int string abc_prepend_shortcut_label 0x7f0e0011
int string abc_search_hint 0x7f0e0012
int string abc_searchview_description_clear 0x7f0e0013
int string abc_searchview_description_query 0x7f0e0014
int string abc_searchview_description_search 0x7f0e0015
int string abc_searchview_description_submit 0x7f0e0016
int string abc_searchview_description_voice 0x7f0e0017
int string abc_shareactionprovider_share_with 0x7f0e0018
int string abc_shareactionprovider_share_with_application 0x7f0e0019
int string abc_toolbar_collapse_description 0x7f0e001a
int string androidx_startup 0x7f0e001b
int string call_notification_answer_action 0x7f0e001c
int string call_notification_answer_video_action 0x7f0e001d
int string call_notification_decline_action 0x7f0e001e
int string call_notification_hang_up_action 0x7f0e001f
int string call_notification_incoming_text 0x7f0e0020
int string call_notification_ongoing_text 0x7f0e0021
int string call_notification_screening_text 0x7f0e0022
int string confirm_device_credential_password 0x7f0e0023
int string copy 0x7f0e0024
int string default_error_msg 0x7f0e0025
int string expand_button_title 0x7f0e0026
int string fingerprint_dialog_touch_sensor 0x7f0e0027
int string fingerprint_error_hw_not_available 0x7f0e0028
int string fingerprint_error_hw_not_present 0x7f0e0029
int string fingerprint_error_lockout 0x7f0e002a
int string fingerprint_error_no_fingerprints 0x7f0e002b
int string fingerprint_error_user_canceled 0x7f0e002c
int string fingerprint_not_recognized 0x7f0e002d
int string generic_error_no_device_credential 0x7f0e002e
int string generic_error_no_keyguard 0x7f0e002f
int string generic_error_user_canceled 0x7f0e0030
int string not_set 0x7f0e0031
int string preference_copied 0x7f0e0032
int string search_menu_title 0x7f0e0033
int string status_bar_notification_info_overflow 0x7f0e0034
int string summary_collapsed_preference_list 0x7f0e0035
int string v7_preference_off 0x7f0e0036
int string v7_preference_on 0x7f0e0037
int style AlertDialog_AppCompat 0x7f0f0000
int style AlertDialog_AppCompat_Light 0x7f0f0001
int style AlertDialogCustom 0x7f0f0002
int style Animation_AppCompat_Dialog 0x7f0f0003
int style Animation_AppCompat_DropDownUp 0x7f0f0004
int style Animation_AppCompat_Tooltip 0x7f0f0005
int style Base_AlertDialog_AppCompat 0x7f0f0006
int style Base_AlertDialog_AppCompat_Light 0x7f0f0007
int style Base_Animation_AppCompat_Dialog 0x7f0f0008
int style Base_Animation_AppCompat_DropDownUp 0x7f0f0009
int style Base_Animation_AppCompat_Tooltip 0x7f0f000a
int style Base_DialogWindowTitle_AppCompat 0x7f0f000b
int style Base_DialogWindowTitleBackground_AppCompat 0x7f0f000c
int style Base_TextAppearance_AppCompat 0x7f0f000d
int style Base_TextAppearance_AppCompat_Body1 0x7f0f000e
int style Base_TextAppearance_AppCompat_Body2 0x7f0f000f
int style Base_TextAppearance_AppCompat_Button 0x7f0f0010
int style Base_TextAppearance_AppCompat_Caption 0x7f0f0011
int style Base_TextAppearance_AppCompat_Display1 0x7f0f0012
int style Base_TextAppearance_AppCompat_Display2 0x7f0f0013
int style Base_TextAppearance_AppCompat_Display3 0x7f0f0014
int style Base_TextAppearance_AppCompat_Display4 0x7f0f0015
int style Base_TextAppearance_AppCompat_Headline 0x7f0f0016
int style Base_TextAppearance_AppCompat_Inverse 0x7f0f0017
int style Base_TextAppearance_AppCompat_Large 0x7f0f0018
int style Base_TextAppearance_AppCompat_Large_Inverse 0x7f0f0019
int style Base_TextAppearance_AppCompat_Light_Widget_PopupMenu_Large 0x7f0f001a
int style Base_TextAppearance_AppCompat_Light_Widget_PopupMenu_Small 0x7f0f001b
int style Base_TextAppearance_AppCompat_Medium 0x7f0f001c
int style Base_TextAppearance_AppCompat_Medium_Inverse 0x7f0f001d
int style Base_TextAppearance_AppCompat_Menu 0x7f0f001e
int style Base_TextAppearance_AppCompat_SearchResult 0x7f0f001f
int style Base_TextAppearance_AppCompat_SearchResult_Subtitle 0x7f0f0020
int style Base_TextAppearance_AppCompat_SearchResult_Title 0x7f0f0021
int style Base_TextAppearance_AppCompat_Small 0x7f0f0022
int style Base_TextAppearance_AppCompat_Small_Inverse 0x7f0f0023
int style Base_TextAppearance_AppCompat_Subhead 0x7f0f0024
int style Base_TextAppearance_AppCompat_Subhead_Inverse 0x7f0f0025
int style Base_TextAppearance_AppCompat_Title 0x7f0f0026
int style Base_TextAppearance_AppCompat_Title_Inverse 0x7f0f0027
int style Base_TextAppearance_AppCompat_Tooltip 0x7f0f0028
int style Base_TextAppearance_AppCompat_Widget_ActionBar_Menu 0x7f0f0029
int style Base_TextAppearance_AppCompat_Widget_ActionBar_Subtitle 0x7f0f002a
int style Base_TextAppearance_AppCompat_Widget_ActionBar_Subtitle_Inverse 0x7f0f002b
int style Base_TextAppearance_AppCompat_Widget_ActionBar_Title 0x7f0f002c
int style Base_TextAppearance_AppCompat_Widget_ActionBar_Title_Inverse 0x7f0f002d
int style Base_TextAppearance_AppCompat_Widget_ActionMode_Subtitle 0x7f0f002e
int style Base_TextAppearance_AppCompat_Widget_ActionMode_Title 0x7f0f002f
int style Base_TextAppearance_AppCompat_Widget_Button 0x7f0f0030
int style Base_TextAppearance_AppCompat_Widget_Button_Borderless_Colored 0x7f0f0031
int style Base_TextAppearance_AppCompat_Widget_Button_Colored 0x7f0f0032
int style Base_TextAppearance_AppCompat_Widget_Button_Inverse 0x7f0f0033
int style Base_TextAppearance_AppCompat_Widget_DropDownItem 0x7f0f0034
int style Base_TextAppearance_AppCompat_Widget_PopupMenu_Header 0x7f0f0035
int style Base_TextAppearance_AppCompat_Widget_PopupMenu_Large 0x7f0f0036
int style Base_TextAppearance_AppCompat_Widget_PopupMenu_Small 0x7f0f0037
int style Base_TextAppearance_AppCompat_Widget_Switch 0x7f0f0038
int style Base_TextAppearance_AppCompat_Widget_TextView_SpinnerItem 0x7f0f0039
int style Base_TextAppearance_Widget_AppCompat_ExpandedMenu_Item 0x7f0f003a
int style Base_TextAppearance_Widget_AppCompat_Toolbar_Subtitle 0x7f0f003b
int style Base_TextAppearance_Widget_AppCompat_Toolbar_Title 0x7f0f003c
int style Base_Theme_AppCompat 0x7f0f003d
int style Base_Theme_AppCompat_CompactMenu 0x7f0f003e
int style Base_Theme_AppCompat_Dialog 0x7f0f003f
int style Base_Theme_AppCompat_Dialog_Alert 0x7f0f0040
int style Base_Theme_AppCompat_Dialog_FixedSize 0x7f0f0041
int style Base_Theme_AppCompat_Dialog_MinWidth 0x7f0f0042
int style Base_Theme_AppCompat_DialogWhenLarge 0x7f0f0043
int style Base_Theme_AppCompat_Light 0x7f0f0044
int style Base_Theme_AppCompat_Light_DarkActionBar 0x7f0f0045
int style Base_Theme_AppCompat_Light_Dialog 0x7f0f0046
int style Base_Theme_AppCompat_Light_Dialog_Alert 0x7f0f0047
int style Base_Theme_AppCompat_Light_Dialog_FixedSize 0x7f0f0048
int style Base_Theme_AppCompat_Light_Dialog_MinWidth 0x7f0f0049
int style Base_Theme_AppCompat_Light_DialogWhenLarge 0x7f0f004a
int style Base_ThemeOverlay_AppCompat 0x7f0f004b
int style Base_ThemeOverlay_AppCompat_ActionBar 0x7f0f004c
int style Base_ThemeOverlay_AppCompat_Dark 0x7f0f004d
int style Base_ThemeOverlay_AppCompat_Dark_ActionBar 0x7f0f004e
int style Base_ThemeOverlay_AppCompat_Dialog 0x7f0f004f
int style Base_ThemeOverlay_AppCompat_Dialog_Alert 0x7f0f0050
int style Base_ThemeOverlay_AppCompat_Light 0x7f0f0051
int style Base_V21_Theme_AppCompat 0x7f0f0052
int style Base_V21_Theme_AppCompat_Dialog 0x7f0f0053
int style Base_V21_Theme_AppCompat_Light 0x7f0f0054
int style Base_V21_Theme_AppCompat_Light_Dialog 0x7f0f0055
int style Base_V21_ThemeOverlay_AppCompat_Dialog 0x7f0f0056
int style Base_V22_Theme_AppCompat 0x7f0f0057
int style Base_V22_Theme_AppCompat_Light 0x7f0f0058
int style Base_V23_Theme_AppCompat 0x7f0f0059
int style Base_V23_Theme_AppCompat_Light 0x7f0f005a
int style Base_V26_Theme_AppCompat 0x7f0f005b
int style Base_V26_Theme_AppCompat_Light 0x7f0f005c
int style Base_V26_Widget_AppCompat_Toolbar 0x7f0f005d
int style Base_V28_Theme_AppCompat 0x7f0f005e
int style Base_V28_Theme_AppCompat_Light 0x7f0f005f
int style Base_V7_Theme_AppCompat 0x7f0f0060
int style Base_V7_Theme_AppCompat_Dialog 0x7f0f0061
int style Base_V7_Theme_AppCompat_Light 0x7f0f0062
int style Base_V7_Theme_AppCompat_Light_Dialog 0x7f0f0063
int style Base_V7_ThemeOverlay_AppCompat_Dialog 0x7f0f0064
int style Base_V7_Widget_AppCompat_AutoCompleteTextView 0x7f0f0065
int style Base_V7_Widget_AppCompat_EditText 0x7f0f0066
int style Base_V7_Widget_AppCompat_Toolbar 0x7f0f0067
int style Base_Widget_AppCompat_ActionBar 0x7f0f0068
int style Base_Widget_AppCompat_ActionBar_Solid 0x7f0f0069
int style Base_Widget_AppCompat_ActionBar_TabBar 0x7f0f006a
int style Base_Widget_AppCompat_ActionBar_TabText 0x7f0f006b
int style Base_Widget_AppCompat_ActionBar_TabView 0x7f0f006c
int style Base_Widget_AppCompat_ActionButton 0x7f0f006d
int style Base_Widget_AppCompat_ActionButton_CloseMode 0x7f0f006e
int style Base_Widget_AppCompat_ActionButton_Overflow 0x7f0f006f
int style Base_Widget_AppCompat_ActionMode 0x7f0f0070
int style Base_Widget_AppCompat_ActivityChooserView 0x7f0f0071
int style Base_Widget_AppCompat_AutoCompleteTextView 0x7f0f0072
int style Base_Widget_AppCompat_Button 0x7f0f0073
int style Base_Widget_AppCompat_Button_Borderless 0x7f0f0074
int style Base_Widget_AppCompat_Button_Borderless_Colored 0x7f0f0075
int style Base_Widget_AppCompat_Button_ButtonBar_AlertDialog 0x7f0f0076
int style Base_Widget_AppCompat_Button_Colored 0x7f0f0077
int style Base_Widget_AppCompat_Button_Small 0x7f0f0078
int style Base_Widget_AppCompat_ButtonBar 0x7f0f0079
int style Base_Widget_AppCompat_ButtonBar_AlertDialog 0x7f0f007a
int style Base_Widget_AppCompat_CompoundButton_CheckBox 0x7f0f007b
int style Base_Widget_AppCompat_CompoundButton_RadioButton 0x7f0f007c
int style Base_Widget_AppCompat_CompoundButton_Switch 0x7f0f007d
int style Base_Widget_AppCompat_DrawerArrowToggle 0x7f0f007e
int style Base_Widget_AppCompat_DrawerArrowToggle_Common 0x7f0f007f
int style Base_Widget_AppCompat_DropDownItem_Spinner 0x7f0f0080
int style Base_Widget_AppCompat_EditText 0x7f0f0081
int style Base_Widget_AppCompat_ImageButton 0x7f0f0082
int style Base_Widget_AppCompat_Light_ActionBar 0x7f0f0083
int style Base_Widget_AppCompat_Light_ActionBar_Solid 0x7f0f0084
int style Base_Widget_AppCompat_Light_ActionBar_TabBar 0x7f0f0085
int style Base_Widget_AppCompat_Light_ActionBar_TabText 0x7f0f0086
int style Base_Widget_AppCompat_Light_ActionBar_TabText_Inverse 0x7f0f0087
int style Base_Widget_AppCompat_Light_ActionBar_TabView 0x7f0f0088
int style Base_Widget_AppCompat_Light_PopupMenu 0x7f0f0089
int style Base_Widget_AppCompat_Light_PopupMenu_Overflow 0x7f0f008a
int style Base_Widget_AppCompat_ListMenuView 0x7f0f008b
int style Base_Widget_AppCompat_ListPopupWindow 0x7f0f008c
int style Base_Widget_AppCompat_ListView 0x7f0f008d
int style Base_Widget_AppCompat_ListView_DropDown 0x7f0f008e
int style Base_Widget_AppCompat_ListView_Menu 0x7f0f008f
int style Base_Widget_AppCompat_PopupMenu 0x7f0f0090
int style Base_Widget_AppCompat_PopupMenu_Overflow 0x7f0f0091
int style Base_Widget_AppCompat_PopupWindow 0x7f0f0092
int style Base_Widget_AppCompat_ProgressBar 0x7f0f0093
int style Base_Widget_AppCompat_ProgressBar_Horizontal 0x7f0f0094
int style Base_Widget_AppCompat_RatingBar 0x7f0f0095
int style Base_Widget_AppCompat_RatingBar_Indicator 0x7f0f0096
int style Base_Widget_AppCompat_RatingBar_Small 0x7f0f0097
int style Base_Widget_AppCompat_SearchView 0x7f0f0098
int style Base_Widget_AppCompat_SearchView_ActionBar 0x7f0f0099
int style Base_Widget_AppCompat_SeekBar 0x7f0f009a
int style Base_Widget_AppCompat_SeekBar_Discrete 0x7f0f009b
int style Base_Widget_AppCompat_Spinner 0x7f0f009c
int style Base_Widget_AppCompat_Spinner_Underlined 0x7f0f009d
int style Base_Widget_AppCompat_TextView 0x7f0f009e
int style Base_Widget_AppCompat_TextView_SpinnerItem 0x7f0f009f
int style Base_Widget_AppCompat_Toolbar 0x7f0f00a0
int style Base_Widget_AppCompat_Toolbar_Button_Navigation 0x7f0f00a1
int style BasePreferenceThemeOverlay 0x7f0f00a2
int style LaunchTheme 0x7f0f00a3
int style NormalTheme 0x7f0f00a4
int style Platform_AppCompat 0x7f0f00a5
int style Platform_AppCompat_Light 0x7f0f00a6
int style Platform_ThemeOverlay_AppCompat 0x7f0f00a7
int style Platform_ThemeOverlay_AppCompat_Dark 0x7f0f00a8
int style Platform_ThemeOverlay_AppCompat_Light 0x7f0f00a9
int style Platform_V21_AppCompat 0x7f0f00aa
int style Platform_V21_AppCompat_Light 0x7f0f00ab
int style Platform_V25_AppCompat 0x7f0f00ac
int style Platform_V25_AppCompat_Light 0x7f0f00ad
int style Platform_Widget_AppCompat_Spinner 0x7f0f00ae
int style Preference 0x7f0f00af
int style Preference_Category 0x7f0f00b0
int style Preference_Category_Material 0x7f0f00b1
int style Preference_CheckBoxPreference 0x7f0f00b2
int style Preference_CheckBoxPreference_Material 0x7f0f00b3
int style Preference_DialogPreference 0x7f0f00b4
int style Preference_DialogPreference_EditTextPreference 0x7f0f00b5
int style Preference_DialogPreference_EditTextPreference_Material 0x7f0f00b6
int style Preference_DialogPreference_Material 0x7f0f00b7
int style Preference_DropDown 0x7f0f00b8
int style Preference_DropDown_Material 0x7f0f00b9
int style Preference_Information 0x7f0f00ba
int style Preference_Information_Material 0x7f0f00bb
int style Preference_Material 0x7f0f00bc
int style Preference_PreferenceScreen 0x7f0f00bd
int style Preference_PreferenceScreen_Material 0x7f0f00be
int style Preference_SeekBarPreference 0x7f0f00bf
int style Preference_SeekBarPreference_Material 0x7f0f00c0
int style Preference_SwitchPreference 0x7f0f00c1
int style Preference_SwitchPreference_Material 0x7f0f00c2
int style Preference_SwitchPreferenceCompat 0x7f0f00c3
int style Preference_SwitchPreferenceCompat_Material 0x7f0f00c4
int style PreferenceCategoryTitleTextStyle 0x7f0f00c5
int style PreferenceFragment 0x7f0f00c6
int style PreferenceFragment_Material 0x7f0f00c7
int style PreferenceFragmentList 0x7f0f00c8
int style PreferenceFragmentList_Material 0x7f0f00c9
int style PreferenceSummaryTextStyle 0x7f0f00ca
int style PreferenceThemeOverlay 0x7f0f00cb
int style PreferenceThemeOverlay_v14 0x7f0f00cc
int style PreferenceThemeOverlay_v14_Material 0x7f0f00cd
int style RtlOverlay_DialogWindowTitle_AppCompat 0x7f0f00ce
int style RtlOverlay_Widget_AppCompat_ActionBar_TitleItem 0x7f0f00cf
int style RtlOverlay_Widget_AppCompat_DialogTitle_Icon 0x7f0f00d0
int style RtlOverlay_Widget_AppCompat_PopupMenuItem 0x7f0f00d1
int style RtlOverlay_Widget_AppCompat_PopupMenuItem_InternalGroup 0x7f0f00d2
int style RtlOverlay_Widget_AppCompat_PopupMenuItem_Shortcut 0x7f0f00d3
int style RtlOverlay_Widget_AppCompat_PopupMenuItem_SubmenuArrow 0x7f0f00d4
int style RtlOverlay_Widget_AppCompat_PopupMenuItem_Text 0x7f0f00d5
int style RtlOverlay_Widget_AppCompat_PopupMenuItem_Title 0x7f0f00d6
int style RtlOverlay_Widget_AppCompat_Search_DropDown 0x7f0f00d7
int style RtlOverlay_Widget_AppCompat_Search_DropDown_Icon1 0x7f0f00d8
int style RtlOverlay_Widget_AppCompat_Search_DropDown_Icon2 0x7f0f00d9
int style RtlOverlay_Widget_AppCompat_Search_DropDown_Query 0x7f0f00da
int style RtlOverlay_Widget_AppCompat_Search_DropDown_Text 0x7f0f00db
int style RtlOverlay_Widget_AppCompat_SearchView_MagIcon 0x7f0f00dc
int style RtlUnderlay_Widget_AppCompat_ActionButton 0x7f0f00dd
int style RtlUnderlay_Widget_AppCompat_ActionButton_Overflow 0x7f0f00de
int style TextAppearance_AppCompat 0x7f0f00df
int style TextAppearance_AppCompat_Body1 0x7f0f00e0
int style TextAppearance_AppCompat_Body2 0x7f0f00e1
int style TextAppearance_AppCompat_Button 0x7f0f00e2
int style TextAppearance_AppCompat_Caption 0x7f0f00e3
int style TextAppearance_AppCompat_Display1 0x7f0f00e4
int style TextAppearance_AppCompat_Display2 0x7f0f00e5
int style TextAppearance_AppCompat_Display3 0x7f0f00e6
int style TextAppearance_AppCompat_Display4 0x7f0f00e7
int style TextAppearance_AppCompat_Headline 0x7f0f00e8
int style TextAppearance_AppCompat_Inverse 0x7f0f00e9
int style TextAppearance_AppCompat_Large 0x7f0f00ea
int style TextAppearance_AppCompat_Large_Inverse 0x7f0f00eb
int style TextAppearance_AppCompat_Light_SearchResult_Subtitle 0x7f0f00ec
int style TextAppearance_AppCompat_Light_SearchResult_Title 0x7f0f00ed
int style TextAppearance_AppCompat_Light_Widget_PopupMenu_Large 0x7f0f00ee
int style TextAppearance_AppCompat_Light_Widget_PopupMenu_Small 0x7f0f00ef
int style TextAppearance_AppCompat_Medium 0x7f0f00f0
int style TextAppearance_AppCompat_Medium_Inverse 0x7f0f00f1
int style TextAppearance_AppCompat_Menu 0x7f0f00f2
int style TextAppearance_AppCompat_SearchResult_Subtitle 0x7f0f00f3
int style TextAppearance_AppCompat_SearchResult_Title 0x7f0f00f4
int style TextAppearance_AppCompat_Small 0x7f0f00f5
int style TextAppearance_AppCompat_Small_Inverse 0x7f0f00f6
int style TextAppearance_AppCompat_Subhead 0x7f0f00f7
int style TextAppearance_AppCompat_Subhead_Inverse 0x7f0f00f8
int style TextAppearance_AppCompat_Title 0x7f0f00f9
int style TextAppearance_AppCompat_Title_Inverse 0x7f0f00fa
int style TextAppearance_AppCompat_Tooltip 0x7f0f00fb
int style TextAppearance_AppCompat_Widget_ActionBar_Menu 0x7f0f00fc
int style TextAppearance_AppCompat_Widget_ActionBar_Subtitle 0x7f0f00fd
int style TextAppearance_AppCompat_Widget_ActionBar_Subtitle_Inverse 0x7f0f00fe
int style TextAppearance_AppCompat_Widget_ActionBar_Title 0x7f0f00ff
int style TextAppearance_AppCompat_Widget_ActionBar_Title_Inverse 0x7f0f0100
int style TextAppearance_AppCompat_Widget_ActionMode_Subtitle 0x7f0f0101
int style TextAppearance_AppCompat_Widget_ActionMode_Subtitle_Inverse 0x7f0f0102
int style TextAppearance_AppCompat_Widget_ActionMode_Title 0x7f0f0103
int style TextAppearance_AppCompat_Widget_ActionMode_Title_Inverse 0x7f0f0104
int style TextAppearance_AppCompat_Widget_Button 0x7f0f0105
int style TextAppearance_AppCompat_Widget_Button_Borderless_Colored 0x7f0f0106
int style TextAppearance_AppCompat_Widget_Button_Colored 0x7f0f0107
int style TextAppearance_AppCompat_Widget_Button_Inverse 0x7f0f0108
int style TextAppearance_AppCompat_Widget_DropDownItem 0x7f0f0109
int style TextAppearance_AppCompat_Widget_PopupMenu_Header 0x7f0f010a
int style TextAppearance_AppCompat_Widget_PopupMenu_Large 0x7f0f010b
int style TextAppearance_AppCompat_Widget_PopupMenu_Small 0x7f0f010c
int style TextAppearance_AppCompat_Widget_Switch 0x7f0f010d
int style TextAppearance_AppCompat_Widget_TextView_SpinnerItem 0x7f0f010e
int style TextAppearance_Compat_Notification 0x7f0f010f
int style TextAppearance_Compat_Notification_Info 0x7f0f0110
int style TextAppearance_Compat_Notification_Line2 0x7f0f0111
int style TextAppearance_Compat_Notification_Time 0x7f0f0112
int style TextAppearance_Compat_Notification_Title 0x7f0f0113
int style TextAppearance_Widget_AppCompat_ExpandedMenu_Item 0x7f0f0114
int style TextAppearance_Widget_AppCompat_Toolbar_Subtitle 0x7f0f0115
int style TextAppearance_Widget_AppCompat_Toolbar_Title 0x7f0f0116
int style Theme_AppCompat 0x7f0f0117
int style Theme_AppCompat_CompactMenu 0x7f0f0118
int style Theme_AppCompat_DayNight 0x7f0f0119
int style Theme_AppCompat_DayNight_DarkActionBar 0x7f0f011a
int style Theme_AppCompat_DayNight_Dialog 0x7f0f011b
int style Theme_AppCompat_DayNight_Dialog_Alert 0x7f0f011c
int style Theme_AppCompat_DayNight_Dialog_MinWidth 0x7f0f011d
int style Theme_AppCompat_DayNight_DialogWhenLarge 0x7f0f011e
int style Theme_AppCompat_DayNight_NoActionBar 0x7f0f011f
int style Theme_AppCompat_Dialog 0x7f0f0120
int style Theme_AppCompat_Dialog_Alert 0x7f0f0121
int style Theme_AppCompat_Dialog_MinWidth 0x7f0f0122
int style Theme_AppCompat_DialogWhenLarge 0x7f0f0123
int style Theme_AppCompat_Empty 0x7f0f0124
int style Theme_AppCompat_Light 0x7f0f0125
int style Theme_AppCompat_Light_DarkActionBar 0x7f0f0126
int style Theme_AppCompat_Light_Dialog 0x7f0f0127
int style Theme_AppCompat_Light_Dialog_Alert 0x7f0f0128
int style Theme_AppCompat_Light_Dialog_MinWidth 0x7f0f0129
int style Theme_AppCompat_Light_DialogWhenLarge 0x7f0f012a
int style Theme_AppCompat_Light_NoActionBar 0x7f0f012b
int style Theme_AppCompat_NoActionBar 0x7f0f012c
int style ThemeOverlay_AppCompat 0x7f0f012d
int style ThemeOverlay_AppCompat_ActionBar 0x7f0f012e
int style ThemeOverlay_AppCompat_Dark 0x7f0f012f
int style ThemeOverlay_AppCompat_Dark_ActionBar 0x7f0f0130
int style ThemeOverlay_AppCompat_DayNight 0x7f0f0131
int style ThemeOverlay_AppCompat_DayNight_ActionBar 0x7f0f0132
int style ThemeOverlay_AppCompat_Dialog 0x7f0f0133
int style ThemeOverlay_AppCompat_Dialog_Alert 0x7f0f0134
int style ThemeOverlay_AppCompat_Light 0x7f0f0135
int style Widget_AppCompat_ActionBar 0x7f0f0136
int style Widget_AppCompat_ActionBar_Solid 0x7f0f0137
int style Widget_AppCompat_ActionBar_TabBar 0x7f0f0138
int style Widget_AppCompat_ActionBar_TabText 0x7f0f0139
int style Widget_AppCompat_ActionBar_TabView 0x7f0f013a
int style Widget_AppCompat_ActionButton 0x7f0f013b
int style Widget_AppCompat_ActionButton_CloseMode 0x7f0f013c
int style Widget_AppCompat_ActionButton_Overflow 0x7f0f013d
int style Widget_AppCompat_ActionMode 0x7f0f013e
int style Widget_AppCompat_ActivityChooserView 0x7f0f013f
int style Widget_AppCompat_AutoCompleteTextView 0x7f0f0140
int style Widget_AppCompat_Button 0x7f0f0141
int style Widget_AppCompat_Button_Borderless 0x7f0f0142
int style Widget_AppCompat_Button_Borderless_Colored 0x7f0f0143
int style Widget_AppCompat_Button_ButtonBar_AlertDialog 0x7f0f0144
int style Widget_AppCompat_Button_Colored 0x7f0f0145
int style Widget_AppCompat_Button_Small 0x7f0f0146
int style Widget_AppCompat_ButtonBar 0x7f0f0147
int style Widget_AppCompat_ButtonBar_AlertDialog 0x7f0f0148
int style Widget_AppCompat_CompoundButton_CheckBox 0x7f0f0149
int style Widget_AppCompat_CompoundButton_RadioButton 0x7f0f014a
int style Widget_AppCompat_CompoundButton_Switch 0x7f0f014b
int style Widget_AppCompat_DrawerArrowToggle 0x7f0f014c
int style Widget_AppCompat_DropDownItem_Spinner 0x7f0f014d
int style Widget_AppCompat_EditText 0x7f0f014e
int style Widget_AppCompat_ImageButton 0x7f0f014f
int style Widget_AppCompat_Light_ActionBar 0x7f0f0150
int style Widget_AppCompat_Light_ActionBar_Solid 0x7f0f0151
int style Widget_AppCompat_Light_ActionBar_Solid_Inverse 0x7f0f0152
int style Widget_AppCompat_Light_ActionBar_TabBar 0x7f0f0153
int style Widget_AppCompat_Light_ActionBar_TabBar_Inverse 0x7f0f0154
int style Widget_AppCompat_Light_ActionBar_TabText 0x7f0f0155
int style Widget_AppCompat_Light_ActionBar_TabText_Inverse 0x7f0f0156
int style Widget_AppCompat_Light_ActionBar_TabView 0x7f0f0157
int style Widget_AppCompat_Light_ActionBar_TabView_Inverse 0x7f0f0158
int style Widget_AppCompat_Light_ActionButton 0x7f0f0159
int style Widget_AppCompat_Light_ActionButton_CloseMode 0x7f0f015a
int style Widget_AppCompat_Light_ActionButton_Overflow 0x7f0f015b
int style Widget_AppCompat_Light_ActionMode_Inverse 0x7f0f015c
int style Widget_AppCompat_Light_ActivityChooserView 0x7f0f015d
int style Widget_AppCompat_Light_AutoCompleteTextView 0x7f0f015e
int style Widget_AppCompat_Light_DropDownItem_Spinner 0x7f0f015f
int style Widget_AppCompat_Light_ListPopupWindow 0x7f0f0160
int style Widget_AppCompat_Light_ListView_DropDown 0x7f0f0161
int style Widget_AppCompat_Light_PopupMenu 0x7f0f0162
int style Widget_AppCompat_Light_PopupMenu_Overflow 0x7f0f0163
int style Widget_AppCompat_Light_SearchView 0x7f0f0164
int style Widget_AppCompat_Light_Spinner_DropDown_ActionBar 0x7f0f0165
int style Widget_AppCompat_ListMenuView 0x7f0f0166
int style Widget_AppCompat_ListPopupWindow 0x7f0f0167
int style Widget_AppCompat_ListView 0x7f0f0168
int style Widget_AppCompat_ListView_DropDown 0x7f0f0169
int style Widget_AppCompat_ListView_Menu 0x7f0f016a
int style Widget_AppCompat_PopupMenu 0x7f0f016b
int style Widget_AppCompat_PopupMenu_Overflow 0x7f0f016c
int style Widget_AppCompat_PopupWindow 0x7f0f016d
int style Widget_AppCompat_ProgressBar 0x7f0f016e
int style Widget_AppCompat_ProgressBar_Horizontal 0x7f0f016f
int style Widget_AppCompat_RatingBar 0x7f0f0170
int style Widget_AppCompat_RatingBar_Indicator 0x7f0f0171
int style Widget_AppCompat_RatingBar_Small 0x7f0f0172
int style Widget_AppCompat_SearchView 0x7f0f0173
int style Widget_AppCompat_SearchView_ActionBar 0x7f0f0174
int style Widget_AppCompat_SeekBar 0x7f0f0175
int style Widget_AppCompat_SeekBar_Discrete 0x7f0f0176
int style Widget_AppCompat_Spinner 0x7f0f0177
int style Widget_AppCompat_Spinner_DropDown 0x7f0f0178
int style Widget_AppCompat_Spinner_DropDown_ActionBar 0x7f0f0179
int style Widget_AppCompat_Spinner_Underlined 0x7f0f017a
int style Widget_AppCompat_TextView 0x7f0f017b
int style Widget_AppCompat_TextView_SpinnerItem 0x7f0f017c
int style Widget_AppCompat_Toolbar 0x7f0f017d
int style Widget_AppCompat_Toolbar_Button_Navigation 0x7f0f017e
int style Widget_Compat_NotificationActionContainer 0x7f0f017f
int style Widget_Compat_NotificationActionText 0x7f0f0180
int style Widget_Support_CoordinatorLayout 0x7f0f0181
int[] styleable ActionBar { 0x7f040039, 0x7f04003a, 0x7f04003b, 0x7f040062, 0x7f040063, 0x7f040064, 0x7f040065, 0x7f040066, 0x7f040067, 0x7f04006a, 0x7f040077, 0x7f040078, 0x7f04008d, 0x7f0400ab, 0x7f0400ac, 0x7f0400ad, 0x7f0400ae, 0x7f0400af, 0x7f0400b5, 0x7f0400ba, 0x7f0400d7, 0x7f0400e2, 0x7f0400f3, 0x7f040102, 0x7f040103, 0x7f040133, 0x7f040136, 0x7f04015a, 0x7f040163 }
int styleable ActionBar_background 0
int styleable ActionBar_backgroundSplit 1
int styleable ActionBar_backgroundStacked 2
int styleable ActionBar_contentInsetEnd 3
int styleable ActionBar_contentInsetEndWithActions 4
int styleable ActionBar_contentInsetLeft 5
int styleable ActionBar_contentInsetRight 6
int styleable ActionBar_contentInsetStart 7
int styleable ActionBar_contentInsetStartWithNavigation 8
int styleable ActionBar_customNavigationLayout 9
int styleable ActionBar_displayOptions 10
int styleable ActionBar_divider 11
int styleable ActionBar_elevation 12
int styleable ActionBar_height 13
int styleable ActionBar_hideOnContentScroll 14
int styleable ActionBar_homeAsUpIndicator 15
int styleable ActionBar_homeLayout 16
int styleable ActionBar_icon 17
int styleable ActionBar_indeterminateProgressStyle 18
int styleable ActionBar_itemPadding 19
int styleable ActionBar_logo 20
int styleable ActionBar_navigationMode 21
int styleable ActionBar_popupTheme 22
int styleable ActionBar_progressBarPadding 23
int styleable ActionBar_progressBarStyle 24
int styleable ActionBar_subtitle 25
int styleable ActionBar_subtitleTextStyle 26
int styleable ActionBar_title 27
int styleable ActionBar_titleTextStyle 28
int[] styleable ActionBarLayout { 0x010100b3 }
int styleable ActionBarLayout_android_layout_gravity 0
int[] styleable ActionMenuItemView { 0x0101013f }
int styleable ActionMenuItemView_android_minWidth 0
int[] styleable ActionMenuView { }
int[] styleable ActionMode { 0x7f040039, 0x7f04003a, 0x7f040052, 0x7f0400ab, 0x7f040136, 0x7f040163 }
int styleable ActionMode_background 0
int styleable ActionMode_backgroundSplit 1
int styleable ActionMode_closeItemLayout 2
int styleable ActionMode_height 3
int styleable ActionMode_subtitleTextStyle 4
int styleable ActionMode_titleTextStyle 5
int[] styleable ActivityChooserView { 0x7f040092, 0x7f0400b6 }
int styleable ActivityChooserView_expandActivityOverflowButtonDrawable 0
int styleable ActivityChooserView_initialActivityCount 1
int[] styleable ActivityFilter { 0x7f040021, 0x7f040023 }
int styleable ActivityFilter_activityAction 0
int styleable ActivityFilter_activityName 1
int[] styleable ActivityRule { 0x7f04002f, 0x7f040143 }
int styleable ActivityRule_alwaysExpand 0
int styleable ActivityRule_tag 1
int[] styleable AlertDialog { 0x010100f2, 0x7f040047, 0x7f040048, 0x7f0400cc, 0x7f0400cd, 0x7f0400df, 0x7f04011d, 0x7f04011e }
int styleable AlertDialog_android_layout 0
int styleable AlertDialog_buttonIconDimen 1
int styleable AlertDialog_buttonPanelSideLayout 2
int styleable AlertDialog_listItemLayout 3
int styleable AlertDialog_listLayout 4
int styleable AlertDialog_multiChoiceItemLayout 5
int styleable AlertDialog_showTitle 6
int styleable AlertDialog_singleChoiceItemLayout 7
int[] styleable AnimatedStateListDrawableCompat { 0x0101011c, 0x01010194, 0x01010195, 0x01010196, 0x0101030c, 0x0101030d }
int styleable AnimatedStateListDrawableCompat_android_dither 0
int styleable AnimatedStateListDrawableCompat_android_visible 1
int styleable AnimatedStateListDrawableCompat_android_variablePadding 2
int styleable AnimatedStateListDrawableCompat_android_constantSize 3
int styleable AnimatedStateListDrawableCompat_android_enterFadeDuration 4
int styleable AnimatedStateListDrawableCompat_android_exitFadeDuration 5
int[] styleable AnimatedStateListDrawableItem { 0x010100d0, 0x01010199 }
int styleable AnimatedStateListDrawableItem_android_id 0
int styleable AnimatedStateListDrawableItem_android_drawable 1
int[] styleable AnimatedStateListDrawableTransition { 0x01010199, 0x01010449, 0x0101044a, 0x0101044b }
int styleable AnimatedStateListDrawableTransition_android_drawable 0
int styleable AnimatedStateListDrawableTransition_android_toId 1
int styleable AnimatedStateListDrawableTransition_android_fromId 2
int styleable AnimatedStateListDrawableTransition_android_reversible 3
int[] styleable AppCompatImageView { 0x01010119, 0x7f04012c, 0x7f040158, 0x7f040159 }
int styleable AppCompatImageView_android_src 0
int styleable AppCompatImageView_srcCompat 1
int styleable AppCompatImageView_tint 2
int styleable AppCompatImageView_tintMode 3
int[] styleable AppCompatSeekBar { 0x01010142, 0x7f040155, 0x7f040156, 0x7f040157 }
int styleable AppCompatSeekBar_android_thumb 0
int styleable AppCompatSeekBar_tickMark 1
int styleable AppCompatSeekBar_tickMarkTint 2
int styleable AppCompatSeekBar_tickMarkTintMode 3
int[] styleable AppCompatTextHelper { 0x01010034, 0x0101016d, 0x0101016e, 0x0101016f, 0x01010170, 0x01010392, 0x01010393 }
int styleable AppCompatTextHelper_android_textAppearance 0
int styleable AppCompatTextHelper_android_drawableTop 1
int styleable AppCompatTextHelper_android_drawableBottom 2
int styleable AppCompatTextHelper_android_drawableLeft 3
int styleable AppCompatTextHelper_android_drawableRight 4
int styleable AppCompatTextHelper_android_drawableStart 5
int styleable AppCompatTextHelper_android_drawableEnd 6
int[] styleable AppCompatTextView { 0x01010034, 0x7f040034, 0x7f040035, 0x7f040036, 0x7f040037, 0x7f040038, 0x7f04007c, 0x7f04007d, 0x7f04007e, 0x7f04007f, 0x7f040081, 0x7f040082, 0x7f040083, 0x7f040084, 0x7f04009b, 0x7f04009d, 0x7f0400a6, 0x7f0400be, 0x7f0400c7, 0x7f040144, 0x7f04014f }
int styleable AppCompatTextView_android_textAppearance 0
int styleable AppCompatTextView_autoSizeMaxTextSize 1
int styleable AppCompatTextView_autoSizeMinTextSize 2
int styleable AppCompatTextView_autoSizePresetSizes 3
int styleable AppCompatTextView_autoSizeStepGranularity 4
int styleable AppCompatTextView_autoSizeTextType 5
int styleable AppCompatTextView_drawableBottomCompat 6
int styleable AppCompatTextView_drawableEndCompat 7
int styleable AppCompatTextView_drawableLeftCompat 8
int styleable AppCompatTextView_drawableRightCompat 9
int styleable AppCompatTextView_drawableStartCompat 10
int styleable AppCompatTextView_drawableTint 11
int styleable AppCompatTextView_drawableTintMode 12
int styleable AppCompatTextView_drawableTopCompat 13
int styleable AppCompatTextView_firstBaselineToTopHeight 14
int styleable AppCompatTextView_fontFamily 15
int styleable AppCompatTextView_fontVariationSettings 16
int styleable AppCompatTextView_lastBaselineToBottomHeight 17
int styleable AppCompatTextView_lineHeight 18
int styleable AppCompatTextView_textAllCaps 19
int styleable AppCompatTextView_textLocale 20
int[] styleable AppCompatTheme { 0x01010057, 0x010100ae, 0x7f040000, 0x7f040001, 0x7f040002, 0x7f040003, 0x7f040004, 0x7f040005, 0x7f040006, 0x7f040007, 0x7f040008, 0x7f040009, 0x7f04000a, 0x7f04000b, 0x7f04000c, 0x7f04000e, 0x7f04000f, 0x7f040010, 0x7f040011, 0x7f040012, 0x7f040013, 0x7f040014, 0x7f040015, 0x7f040016, 0x7f040017, 0x7f040018, 0x7f040019, 0x7f04001a, 0x7f04001b, 0x7f04001c, 0x7f04001d, 0x7f04001e, 0x7f040022, 0x7f040025, 0x7f040026, 0x7f040027, 0x7f040028, 0x7f040033, 0x7f04003f, 0x7f040040, 0x7f040041, 0x7f040042, 0x7f040043, 0x7f040044, 0x7f040049, 0x7f04004a, 0x7f04004e, 0x7f04004f, 0x7f040056, 0x7f040057, 0x7f040058, 0x7f040059, 0x7f04005a, 0x7f04005b, 0x7f04005c, 0x7f04005d, 0x7f04005e, 0x7f04005f, 0x7f040068, 0x7f04006e, 0x7f040073, 0x7f040074, 0x7f040079, 0x7f04007b, 0x7f040086, 0x7f040087, 0x7f040089, 0x7f04008a, 0x7f04008c, 0x7f0400ad, 0x7f0400b4, 0x7f0400c8, 0x7f0400c9, 0x7f0400ca, 0x7f0400cb, 0x7f0400ce, 0x7f0400cf, 0x7f0400d0, 0x7f0400d1, 0x7f0400d2, 0x7f0400d3, 0x7f0400d4, 0x7f0400d5, 0x7f0400d6, 0x7f0400ed, 0x7f0400ee, 0x7f0400ef, 0x7f0400f2, 0x7f0400f4, 0x7f040107, 0x7f040108, 0x7f040109, 0x7f04010a, 0x7f04010e, 0x7f040113, 0x7f040115, 0x7f040116, 0x7f040122, 0x7f040123, 0x7f04013f, 0x7f040145, 0x7f040146, 0x7f040147, 0x7f040148, 0x7f040149, 0x7f04014a, 0x7f04014b, 0x7f04014c, 0x7f04014d, 0x7f04014e, 0x7f040164, 0x7f040165, 0x7f040166, 0x7f040167, 0x7f04016f, 0x7f040172, 0x7f040173, 0x7f040174, 0x7f040175, 0x7f040176, 0x7f040177, 0x7f040178, 0x7f040179, 0x7f04017a, 0x7f04017b }
int styleable AppCompatTheme_android_windowIsFloating 0
int styleable AppCompatTheme_android_windowAnimationStyle 1
int styleable AppCompatTheme_actionBarDivider 2
int styleable AppCompatTheme_actionBarItemBackground 3
int styleable AppCompatTheme_actionBarPopupTheme 4
int styleable AppCompatTheme_actionBarSize 5
int styleable AppCompatTheme_actionBarSplitStyle 6
int styleable AppCompatTheme_actionBarStyle 7
int styleable AppCompatTheme_actionBarTabBarStyle 8
int styleable AppCompatTheme_actionBarTabStyle 9
int styleable AppCompatTheme_actionBarTabTextStyle 10
int styleable AppCompatTheme_actionBarTheme 11
int styleable AppCompatTheme_actionBarWidgetTheme 12
int styleable AppCompatTheme_actionButtonStyle 13
int styleable AppCompatTheme_actionDropDownStyle 14
int styleable AppCompatTheme_actionMenuTextAppearance 15
int styleable AppCompatTheme_actionMenuTextColor 16
int styleable AppCompatTheme_actionModeBackground 17
int styleable AppCompatTheme_actionModeCloseButtonStyle 18
int styleable AppCompatTheme_actionModeCloseDrawable 19
int styleable AppCompatTheme_actionModeCopyDrawable 20
int styleable AppCompatTheme_actionModeCutDrawable 21
int styleable AppCompatTheme_actionModeFindDrawable 22
int styleable AppCompatTheme_actionModePasteDrawable 23
int styleable AppCompatTheme_actionModePopupWindowStyle 24
int styleable AppCompatTheme_actionModeSelectAllDrawable 25
int styleable AppCompatTheme_actionModeShareDrawable 26
int styleable AppCompatTheme_actionModeSplitBackground 27
int styleable AppCompatTheme_actionModeStyle 28
int styleable AppCompatTheme_actionModeWebSearchDrawable 29
int styleable AppCompatTheme_actionOverflowButtonStyle 30
int styleable AppCompatTheme_actionOverflowMenuStyle 31
int styleable AppCompatTheme_activityChooserViewStyle 32
int styleable AppCompatTheme_alertDialogButtonGroupStyle 33
int styleable AppCompatTheme_alertDialogCenterButtons 34
int styleable AppCompatTheme_alertDialogStyle 35
int styleable AppCompatTheme_alertDialogTheme 36
int styleable AppCompatTheme_autoCompleteTextViewStyle 37
int styleable AppCompatTheme_borderlessButtonStyle 38
int styleable AppCompatTheme_buttonBarButtonStyle 39
int styleable AppCompatTheme_buttonBarNegativeButtonStyle 40
int styleable AppCompatTheme_buttonBarNeutralButtonStyle 41
int styleable AppCompatTheme_buttonBarPositiveButtonStyle 42
int styleable AppCompatTheme_buttonBarStyle 43
int styleable AppCompatTheme_buttonStyle 44
int styleable AppCompatTheme_buttonStyleSmall 45
int styleable AppCompatTheme_checkboxStyle 46
int styleable AppCompatTheme_checkedTextViewStyle 47
int styleable AppCompatTheme_colorAccent 48
int styleable AppCompatTheme_colorBackgroundFloating 49
int styleable AppCompatTheme_colorButtonNormal 50
int styleable AppCompatTheme_colorControlActivated 51
int styleable AppCompatTheme_colorControlHighlight 52
int styleable AppCompatTheme_colorControlNormal 53
int styleable AppCompatTheme_colorError 54
int styleable AppCompatTheme_colorPrimary 55
int styleable AppCompatTheme_colorPrimaryDark 56
int styleable AppCompatTheme_colorSwitchThumbNormal 57
int styleable AppCompatTheme_controlBackground 58
int styleable AppCompatTheme_dialogCornerRadius 59
int styleable AppCompatTheme_dialogPreferredPadding 60
int styleable AppCompatTheme_dialogTheme 61
int styleable AppCompatTheme_dividerHorizontal 62
int styleable AppCompatTheme_dividerVertical 63
int styleable AppCompatTheme_dropDownListViewStyle 64
int styleable AppCompatTheme_dropdownListPreferredItemHeight 65
int styleable AppCompatTheme_editTextBackground 66
int styleable AppCompatTheme_editTextColor 67
int styleable AppCompatTheme_editTextStyle 68
int styleable AppCompatTheme_homeAsUpIndicator 69
int styleable AppCompatTheme_imageButtonStyle 70
int styleable AppCompatTheme_listChoiceBackgroundIndicator 71
int styleable AppCompatTheme_listChoiceIndicatorMultipleAnimated 72
int styleable AppCompatTheme_listChoiceIndicatorSingleAnimated 73
int styleable AppCompatTheme_listDividerAlertDialog 74
int styleable AppCompatTheme_listMenuViewStyle 75
int styleable AppCompatTheme_listPopupWindowStyle 76
int styleable AppCompatTheme_listPreferredItemHeight 77
int styleable AppCompatTheme_listPreferredItemHeightLarge 78
int styleable AppCompatTheme_listPreferredItemHeightSmall 79
int styleable AppCompatTheme_listPreferredItemPaddingEnd 80
int styleable AppCompatTheme_listPreferredItemPaddingLeft 81
int styleable AppCompatTheme_listPreferredItemPaddingRight 82
int styleable AppCompatTheme_listPreferredItemPaddingStart 83
int styleable AppCompatTheme_panelBackground 84
int styleable AppCompatTheme_panelMenuListTheme 85
int styleable AppCompatTheme_panelMenuListWidth 86
int styleable AppCompatTheme_popupMenuStyle 87
int styleable AppCompatTheme_popupWindowStyle 88
int styleable AppCompatTheme_radioButtonStyle 89
int styleable AppCompatTheme_ratingBarStyle 90
int styleable AppCompatTheme_ratingBarStyleIndicator 91
int styleable AppCompatTheme_ratingBarStyleSmall 92
int styleable AppCompatTheme_searchViewStyle 93
int styleable AppCompatTheme_seekBarStyle 94
int styleable AppCompatTheme_selectableItemBackground 95
int styleable AppCompatTheme_selectableItemBackgroundBorderless 96
int styleable AppCompatTheme_spinnerDropDownItemStyle 97
int styleable AppCompatTheme_spinnerStyle 98
int styleable AppCompatTheme_switchStyle 99
int styleable AppCompatTheme_textAppearanceLargePopupMenu 100
int styleable AppCompatTheme_textAppearanceListItem 101
int styleable AppCompatTheme_textAppearanceListItemSecondary 102
int styleable AppCompatTheme_textAppearanceListItemSmall 103
int styleable AppCompatTheme_textAppearancePopupMenuHeader 104
int styleable AppCompatTheme_textAppearanceSearchResultSubtitle 105
int styleable AppCompatTheme_textAppearanceSearchResultTitle 106
int styleable AppCompatTheme_textAppearanceSmallPopupMenu 107
int styleable AppCompatTheme_textColorAlertDialogListItem 108
int styleable AppCompatTheme_textColorSearchUrl 109
int styleable AppCompatTheme_toolbarNavigationButtonStyle 110
int styleable AppCompatTheme_toolbarStyle 111
int styleable AppCompatTheme_tooltipForegroundColor 112
int styleable AppCompatTheme_tooltipFrameBackground 113
int styleable AppCompatTheme_viewInflaterClass 114
int styleable AppCompatTheme_windowActionBar 115
int styleable AppCompatTheme_windowActionBarOverlay 116
int styleable AppCompatTheme_windowActionModeOverlay 117
int styleable AppCompatTheme_windowFixedHeightMajor 118
int styleable AppCompatTheme_windowFixedHeightMinor 119
int styleable AppCompatTheme_windowFixedWidthMajor 120
int styleable AppCompatTheme_windowFixedWidthMinor 121
int styleable AppCompatTheme_windowMinWidthMajor 122
int styleable AppCompatTheme_windowMinWidthMinor 123
int styleable AppCompatTheme_windowNoTitle 124
int[] styleable BackgroundStyle { 0x0101030e, 0x7f040115 }
int styleable BackgroundStyle_android_selectableItemBackground 0
int styleable BackgroundStyle_selectableItemBackground 1
int[] styleable ButtonBarLayout { 0x7f04002c }
int styleable ButtonBarLayout_allowStacking 0
int[] styleable Capability { 0x7f040106, 0x7f040117 }
int styleable Capability_queryPatterns 0
int styleable Capability_shortcutMatchRequired 1
int[] styleable CheckBoxPreference { 0x010101ef, 0x010101f0, 0x010101f1, 0x7f040076, 0x7f040139, 0x7f04013a }
int styleable CheckBoxPreference_android_summaryOn 0
int styleable CheckBoxPreference_android_summaryOff 1
int styleable CheckBoxPreference_android_disableDependentsState 2
int styleable CheckBoxPreference_disableDependentsState 3
int styleable CheckBoxPreference_summaryOff 4
int styleable CheckBoxPreference_summaryOn 5
int[] styleable ColorStateListItem { 0x010101a5, 0x0101031f, 0x01010647, 0x7f04002d, 0x7f0400bd }
int styleable ColorStateListItem_android_color 0
int styleable ColorStateListItem_android_alpha 1
int styleable ColorStateListItem_android_lStar 2
int styleable ColorStateListItem_alpha 3
int styleable ColorStateListItem_lStar 4
int[] styleable CompoundButton { 0x01010107, 0x7f040045, 0x7f04004b, 0x7f04004c }
int styleable CompoundButton_android_button 0
int styleable CompoundButton_buttonCompat 1
int styleable CompoundButton_buttonTint 2
int styleable CompoundButton_buttonTintMode 3
int[] styleable CoordinatorLayout { 0x7f0400bc, 0x7f04012f }
int styleable CoordinatorLayout_keylines 0
int styleable CoordinatorLayout_statusBarBackground 1
int[] styleable CoordinatorLayout_Layout { 0x010100b3, 0x7f0400c1, 0x7f0400c2, 0x7f0400c3, 0x7f0400c4, 0x7f0400c5, 0x7f0400c6 }
int styleable CoordinatorLayout_Layout_android_layout_gravity 0
int styleable CoordinatorLayout_Layout_layout_anchor 1
int styleable CoordinatorLayout_Layout_layout_anchorGravity 2
int styleable CoordinatorLayout_Layout_layout_behavior 3
int styleable CoordinatorLayout_Layout_layout_dodgeInsetEdges 4
int styleable CoordinatorLayout_Layout_layout_insetEdge 5
int styleable CoordinatorLayout_Layout_layout_keyline 6
int[] styleable DialogPreference { 0x010101f2, 0x010101f3, 0x010101f4, 0x010101f5, 0x010101f6, 0x010101f7, 0x7f04006f, 0x7f040070, 0x7f040071, 0x7f040075, 0x7f0400e3, 0x7f0400f5 }
int styleable DialogPreference_android_dialogTitle 0
int styleable DialogPreference_android_dialogMessage 1
int styleable DialogPreference_android_dialogIcon 2
int styleable DialogPreference_android_positiveButtonText 3
int styleable DialogPreference_android_negativeButtonText 4
int styleable DialogPreference_android_dialogLayout 5
int styleable DialogPreference_dialogIcon 6
int styleable DialogPreference_dialogLayout 7
int styleable DialogPreference_dialogMessage 8
int styleable DialogPreference_dialogTitle 9
int styleable DialogPreference_negativeButtonText 10
int styleable DialogPreference_positiveButtonText 11
int[] styleable DrawerArrowToggle { 0x7f040031, 0x7f040032, 0x7f04003e, 0x7f040055, 0x7f040080, 0x7f0400a9, 0x7f040121, 0x7f040151 }
int styleable DrawerArrowToggle_arrowHeadLength 0
int styleable DrawerArrowToggle_arrowShaftLength 1
int styleable DrawerArrowToggle_barLength 2
int styleable DrawerArrowToggle_color 3
int styleable DrawerArrowToggle_drawableSize 4
int styleable DrawerArrowToggle_gapBetweenBars 5
int styleable DrawerArrowToggle_spinBars 6
int styleable DrawerArrowToggle_thickness 7
int[] styleable EditTextPreference { 0x7f04016e }
int styleable EditTextPreference_useSimpleSummaryProvider 0
int[] styleable FontFamily { 0x7f04009e, 0x7f04009f, 0x7f0400a0, 0x7f0400a1, 0x7f0400a2, 0x7f0400a3, 0x7f0400a4 }
int styleable FontFamily_fontProviderAuthority 0
int styleable FontFamily_fontProviderCerts 1
int styleable FontFamily_fontProviderFetchStrategy 2
int styleable FontFamily_fontProviderFetchTimeout 3
int styleable FontFamily_fontProviderPackage 4
int styleable FontFamily_fontProviderQuery 5
int styleable FontFamily_fontProviderSystemFontFamily 6
int[] styleable FontFamilyFont { 0x01010532, 0x01010533, 0x0101053f, 0x0101056f, 0x01010570, 0x7f04009c, 0x7f0400a5, 0x7f0400a6, 0x7f0400a7, 0x7f04016c }
int styleable FontFamilyFont_android_font 0
int styleable FontFamilyFont_android_fontWeight 1
int styleable FontFamilyFont_android_fontStyle 2
int styleable FontFamilyFont_android_ttcIndex 3
int styleable FontFamilyFont_android_fontVariationSettings 4
int styleable FontFamilyFont_font 5
int styleable FontFamilyFont_fontStyle 6
int styleable FontFamilyFont_fontVariationSettings 7
int styleable FontFamilyFont_fontWeight 8
int styleable FontFamilyFont_ttcIndex 9
int[] styleable Fragment { 0x01010003, 0x010100d0, 0x010100d1 }
int styleable Fragment_android_name 0
int styleable Fragment_android_id 1
int styleable Fragment_android_tag 2
int[] styleable FragmentContainerView { 0x01010003, 0x010100d1 }
int styleable FragmentContainerView_android_name 0
int styleable FragmentContainerView_android_tag 1
int[] styleable GradientColor { 0x0101019d, 0x0101019e, 0x010101a1, 0x010101a2, 0x010101a3, 0x010101a4, 0x01010201, 0x0101020b, 0x01010510, 0x01010511, 0x01010512, 0x01010513 }
int styleable GradientColor_android_startColor 0
int styleable GradientColor_android_endColor 1
int styleable GradientColor_android_type 2
int styleable GradientColor_android_centerX 3
int styleable GradientColor_android_centerY 4
int styleable GradientColor_android_gradientRadius 5
int styleable GradientColor_android_tileMode 6
int styleable GradientColor_android_centerColor 7
int styleable GradientColor_android_startX 8
int styleable GradientColor_android_startY 9
int styleable GradientColor_android_endX 10
int styleable GradientColor_android_endY 11
int[] styleable GradientColorItem { 0x010101a5, 0x01010514 }
int styleable GradientColorItem_android_color 0
int styleable GradientColorItem_android_offset 1
int[] styleable LinearLayoutCompat { 0x010100af, 0x010100c4, 0x01010126, 0x01010127, 0x01010128, 0x7f040078, 0x7f04007a, 0x7f0400dc, 0x7f04011a }
int styleable LinearLayoutCompat_android_gravity 0
int styleable LinearLayoutCompat_android_orientation 1
int styleable LinearLayoutCompat_android_baselineAligned 2
int styleable LinearLayoutCompat_android_baselineAlignedChildIndex 3
int styleable LinearLayoutCompat_android_weightSum 4
int styleable LinearLayoutCompat_divider 5
int styleable LinearLayoutCompat_dividerPadding 6
int styleable LinearLayoutCompat_measureWithLargestChild 7
int styleable LinearLayoutCompat_showDividers 8
int[] styleable LinearLayoutCompat_Layout { 0x010100b3, 0x010100f4, 0x010100f5, 0x01010181 }
int styleable LinearLayoutCompat_Layout_android_layout_gravity 0
int styleable LinearLayoutCompat_Layout_android_layout_width 1
int styleable LinearLayoutCompat_Layout_android_layout_height 2
int styleable LinearLayoutCompat_Layout_android_layout_weight 3
int[] styleable ListPopupWindow { 0x010102ac, 0x010102ad }
int styleable ListPopupWindow_android_dropDownHorizontalOffset 0
int styleable ListPopupWindow_android_dropDownVerticalOffset 1
int[] styleable ListPreference { 0x010100b2, 0x010101f8, 0x7f040090, 0x7f040091, 0x7f04016e }
int styleable ListPreference_android_entries 0
int styleable ListPreference_android_entryValues 1
int styleable ListPreference_entries 2
int styleable ListPreference_entryValues 3
int styleable ListPreference_useSimpleSummaryProvider 4
int[] styleable MenuGroup { 0x0101000e, 0x010100d0, 0x01010194, 0x010101de, 0x010101df, 0x010101e0 }
int styleable MenuGroup_android_enabled 0
int styleable MenuGroup_android_id 1
int styleable MenuGroup_android_visible 2
int styleable MenuGroup_android_menuCategory 3
int styleable MenuGroup_android_orderInCategory 4
int styleable MenuGroup_android_checkableBehavior 5
int[] styleable MenuItem { 0x01010002, 0x0101000e, 0x010100d0, 0x01010106, 0x01010194, 0x010101de, 0x010101df, 0x010101e1, 0x010101e2, 0x010101e3, 0x010101e4, 0x010101e5, 0x0101026f, 0x7f04000d, 0x7f04001f, 0x7f040020, 0x7f04002e, 0x7f040061, 0x7f0400b1, 0x7f0400b2, 0x7f0400e5, 0x7f040119, 0x7f040168 }
int styleable MenuItem_android_icon 0
int styleable MenuItem_android_enabled 1
int styleable MenuItem_android_id 2
int styleable MenuItem_android_checked 3
int styleable MenuItem_android_visible 4
int styleable MenuItem_android_menuCategory 5
int styleable MenuItem_android_orderInCategory 6
int styleable MenuItem_android_title 7
int styleable MenuItem_android_titleCondensed 8
int styleable MenuItem_android_alphabeticShortcut 9
int styleable MenuItem_android_numericShortcut 10
int styleable MenuItem_android_checkable 11
int styleable MenuItem_android_onClick 12
int styleable MenuItem_actionLayout 13
int styleable MenuItem_actionProviderClass 14
int styleable MenuItem_actionViewClass 15
int styleable MenuItem_alphabeticModifiers 16
int styleable MenuItem_contentDescription 17
int styleable MenuItem_iconTint 18
int styleable MenuItem_iconTintMode 19
int styleable MenuItem_numericModifiers 20
int styleable MenuItem_showAsAction 21
int styleable MenuItem_tooltipText 22
int[] styleable MenuView { 0x010100ae, 0x0101012c, 0x0101012d, 0x0101012e, 0x0101012f, 0x01010130, 0x01010131, 0x7f040100, 0x7f040131 }
int styleable MenuView_android_windowAnimationStyle 0
int styleable MenuView_android_itemTextAppearance 1
int styleable MenuView_android_horizontalDivider 2
int styleable MenuView_android_verticalDivider 3
int styleable MenuView_android_headerBackground 4
int styleable MenuView_android_itemBackground 5
int styleable MenuView_android_itemIconDisabledAlpha 6
int styleable MenuView_preserveIconSpacing 7
int styleable MenuView_subMenuArrow 8
int[] styleable MultiSelectListPreference { 0x010100b2, 0x010101f8, 0x7f040090, 0x7f040091 }
int styleable MultiSelectListPreference_android_entries 0
int styleable MultiSelectListPreference_android_entryValues 1
int styleable MultiSelectListPreference_entries 2
int styleable MultiSelectListPreference_entryValues 3
int[] styleable PopupWindow { 0x01010176, 0x010102c9, 0x7f0400e8 }
int styleable PopupWindow_android_popupBackground 0
int styleable PopupWindow_android_popupAnimationStyle 1
int styleable PopupWindow_overlapAnchor 2
int[] styleable PopupWindowBackgroundState { 0x7f04012e }
int styleable PopupWindowBackgroundState_state_above_anchor 0
int[] styleable Preference { 0x01010002, 0x0101000d, 0x0101000e, 0x010100f2, 0x010101e1, 0x010101e6, 0x010101e8, 0x010101e9, 0x010101ea, 0x010101eb, 0x010101ec, 0x010101ed, 0x010101ee, 0x010102e3, 0x0101055c, 0x01010561, 0x7f040029, 0x7f04002b, 0x7f04006c, 0x7f04006d, 0x7f04008e, 0x7f04008f, 0x7f0400a8, 0x7f0400af, 0x7f0400b0, 0x7f0400b9, 0x7f0400bb, 0x7f0400bf, 0x7f0400e6, 0x7f0400f0, 0x7f040114, 0x7f040118, 0x7f04011f, 0x7f040138, 0x7f04015a, 0x7f040171 }
int styleable Preference_android_icon 0
int styleable Preference_android_persistent 1
int styleable Preference_android_enabled 2
int styleable Preference_android_layout 3
int styleable Preference_android_title 4
int styleable Preference_android_selectable 5
int styleable Preference_android_key 6
int styleable Preference_android_summary 7
int styleable Preference_android_order 8
int styleable Preference_android_widgetLayout 9
int styleable Preference_android_dependency 10
int styleable Preference_android_defaultValue 11
int styleable Preference_android_shouldDisableView 12
int styleable Preference_android_fragment 13
int styleable Preference_android_singleLineTitle 14
int styleable Preference_android_iconSpaceReserved 15
int styleable Preference_allowDividerAbove 16
int styleable Preference_allowDividerBelow 17
int styleable Preference_defaultValue 18
int styleable Preference_dependency 19
int styleable Preference_enableCopying 20
int styleable Preference_enabled 21
int styleable Preference_fragment 22
int styleable Preference_icon 23
int styleable Preference_iconSpaceReserved 24
int styleable Preference_isPreferenceVisible 25
int styleable Preference_key 26
int styleable Preference_layout 27
int styleable Preference_order 28
int styleable Preference_persistent 29
int styleable Preference_selectable 30
int styleable Preference_shouldDisableView 31
int styleable Preference_singleLineTitle 32
int styleable Preference_summary 33
int styleable Preference_title 34
int styleable Preference_widgetLayout 35
int[] styleable PreferenceFragment { 0x010100f2, 0x01010129, 0x0101012a, 0x7f04002a }
int styleable PreferenceFragment_android_layout 0
int styleable PreferenceFragment_android_divider 1
int styleable PreferenceFragment_android_dividerHeight 2
int styleable PreferenceFragment_allowDividerAfterLastItem 3
int[] styleable PreferenceFragmentCompat { 0x010100f2, 0x01010129, 0x0101012a, 0x7f04002a }
int styleable PreferenceFragmentCompat_android_layout 0
int styleable PreferenceFragmentCompat_android_divider 1
int styleable PreferenceFragmentCompat_android_dividerHeight 2
int styleable PreferenceFragmentCompat_allowDividerAfterLastItem 3
int[] styleable PreferenceGroup { 0x010101e7, 0x7f0400b7, 0x7f0400e7 }
int styleable PreferenceGroup_android_orderingFromXml 0
int styleable PreferenceGroup_initialExpandedChildrenCount 1
int styleable PreferenceGroup_orderingFromXml 2
int[] styleable PreferenceImageView { 0x0101011f, 0x01010120, 0x7f0400da, 0x7f0400db }
int styleable PreferenceImageView_android_maxWidth 0
int styleable PreferenceImageView_android_maxHeight 1
int styleable PreferenceImageView_maxHeight 2
int styleable PreferenceImageView_maxWidth 3
int[] styleable PreferenceTheme { 0x7f04004d, 0x7f040072, 0x7f040088, 0x7f04008b, 0x7f0400f6, 0x7f0400f7, 0x7f0400f8, 0x7f0400f9, 0x7f0400fa, 0x7f0400fb, 0x7f0400fc, 0x7f0400fd, 0x7f0400fe, 0x7f0400ff, 0x7f040112, 0x7f04013d, 0x7f04013e }
int styleable PreferenceTheme_checkBoxPreferenceStyle 0
int styleable PreferenceTheme_dialogPreferenceStyle 1
int styleable PreferenceTheme_dropdownPreferenceStyle 2
int styleable PreferenceTheme_editTextPreferenceStyle 3
int styleable PreferenceTheme_preferenceCategoryStyle 4
int styleable PreferenceTheme_preferenceCategoryTitleTextAppearance 5
int styleable PreferenceTheme_preferenceCategoryTitleTextColor 6
int styleable PreferenceTheme_preferenceFragmentCompatStyle 7
int styleable PreferenceTheme_preferenceFragmentListStyle 8
int styleable PreferenceTheme_preferenceFragmentStyle 9
int styleable PreferenceTheme_preferenceInformationStyle 10
int styleable PreferenceTheme_preferenceScreenStyle 11
int styleable PreferenceTheme_preferenceStyle 12
int styleable PreferenceTheme_preferenceTheme 13
int styleable PreferenceTheme_seekBarPreferenceStyle 14
int styleable PreferenceTheme_switchPreferenceCompatStyle 15
int styleable PreferenceTheme_switchPreferenceStyle 16
int[] styleable RecycleListView { 0x7f0400e9, 0x7f0400ec }
int styleable RecycleListView_paddingBottomNoButtons 0
int styleable RecycleListView_paddingTopNoTitle 1
int[] styleable RecyclerView { 0x010100c4, 0x010100f1, 0x7f040093, 0x7f040094, 0x7f040095, 0x7f040096, 0x7f040097, 0x7f0400c0, 0x7f04010b, 0x7f040120, 0x7f04012d }
int styleable RecyclerView_android_orientation 0
int styleable RecyclerView_android_descendantFocusability 1
int styleable RecyclerView_fastScrollEnabled 2
int styleable RecyclerView_fastScrollHorizontalThumbDrawable 3
int styleable RecyclerView_fastScrollHorizontalTrackDrawable 4
int styleable RecyclerView_fastScrollVerticalThumbDrawable 5
int styleable RecyclerView_fastScrollVerticalTrackDrawable 6
int styleable RecyclerView_layoutManager 7
int styleable RecyclerView_reverseLayout 8
int styleable RecyclerView_spanCount 9
int styleable RecyclerView_stackFromEnd 10
int[] styleable SearchView { 0x010100da, 0x0101011f, 0x01010220, 0x01010264, 0x7f040051, 0x7f040060, 0x7f04006b, 0x7f0400aa, 0x7f0400b3, 0x7f0400bf, 0x7f040104, 0x7f040105, 0x7f04010c, 0x7f04010d, 0x7f040132, 0x7f040137, 0x7f040170 }
int styleable SearchView_android_focusable 0
int styleable SearchView_android_maxWidth 1
int styleable SearchView_android_inputType 2
int styleable SearchView_android_imeOptions 3
int styleable SearchView_closeIcon 4
int styleable SearchView_commitIcon 5
int styleable SearchView_defaultQueryHint 6
int styleable SearchView_goIcon 7
int styleable SearchView_iconifiedByDefault 8
int styleable SearchView_layout 9
int styleable SearchView_queryBackground 10
int styleable SearchView_queryHint 11
int styleable SearchView_searchHintIcon 12
int styleable SearchView_searchIcon 13
int styleable SearchView_submitBackground 14
int styleable SearchView_suggestionRowLayout 15
int styleable SearchView_voiceIcon 16
int[] styleable SeekBarPreference { 0x010100f2, 0x01010136, 0x7f040024, 0x7f0400de, 0x7f040111, 0x7f04011b, 0x7f04016d }
int styleable SeekBarPreference_android_layout 0
int styleable SeekBarPreference_android_max 1
int styleable SeekBarPreference_adjustable 2
int styleable SeekBarPreference_min 3
int styleable SeekBarPreference_seekBarIncrement 4
int styleable SeekBarPreference_showSeekBarValue 5
int styleable SeekBarPreference_updatesContinuously 6
int[] styleable Spinner { 0x010100b2, 0x01010176, 0x0101017b, 0x01010262, 0x7f0400f3 }
int styleable Spinner_android_entries 0
int styleable Spinner_android_popupBackground 1
int styleable Spinner_android_prompt 2
int styleable Spinner_android_dropDownWidth 3
int styleable Spinner_popupTheme 4
int[] styleable SplitPairFilter { 0x7f040101, 0x7f04010f, 0x7f040110 }
int styleable SplitPairFilter_primaryActivityName 0
int styleable SplitPairFilter_secondaryActivityAction 1
int styleable SplitPairFilter_secondaryActivityName 2
int[] styleable SplitPairRule { 0x7f040030, 0x7f040050, 0x7f040099, 0x7f04009a, 0x7f040124, 0x7f040125, 0x7f040126, 0x7f040127, 0x7f040128, 0x7f040129, 0x7f04012a, 0x7f040143 }
int styleable SplitPairRule_animationBackgroundColor 0
int styleable SplitPairRule_clearTop 1
int styleable SplitPairRule_finishPrimaryWithSecondary 2
int styleable SplitPairRule_finishSecondaryWithPrimary 3
int styleable SplitPairRule_splitLayoutDirection 4
int styleable SplitPairRule_splitMaxAspectRatioInLandscape 5
int styleable SplitPairRule_splitMaxAspectRatioInPortrait 6
int styleable SplitPairRule_splitMinHeightDp 7
int styleable SplitPairRule_splitMinSmallestWidthDp 8
int styleable SplitPairRule_splitMinWidthDp 9
int styleable SplitPairRule_splitRatio 10
int styleable SplitPairRule_tag 11
int[] styleable SplitPlaceholderRule { 0x7f040030, 0x7f040098, 0x7f0400f1, 0x7f040124, 0x7f040125, 0x7f040126, 0x7f040127, 0x7f040128, 0x7f040129, 0x7f04012a, 0x7f040130, 0x7f040143 }
int styleable SplitPlaceholderRule_animationBackgroundColor 0
int styleable SplitPlaceholderRule_finishPrimaryWithPlaceholder 1
int styleable SplitPlaceholderRule_placeholderActivityName 2
int styleable SplitPlaceholderRule_splitLayoutDirection 3
int styleable SplitPlaceholderRule_splitMaxAspectRatioInLandscape 4
int styleable SplitPlaceholderRule_splitMaxAspectRatioInPortrait 5
int styleable SplitPlaceholderRule_splitMinHeightDp 6
int styleable SplitPlaceholderRule_splitMinSmallestWidthDp 7
int styleable SplitPlaceholderRule_splitMinWidthDp 8
int styleable SplitPlaceholderRule_splitRatio 9
int styleable SplitPlaceholderRule_stickyPlaceholder 10
int styleable SplitPlaceholderRule_tag 11
int[] styleable StateListDrawable { 0x0101011c, 0x01010194, 0x01010195, 0x01010196, 0x0101030c, 0x0101030d }
int styleable StateListDrawable_android_dither 0
int styleable StateListDrawable_android_visible 1
int styleable StateListDrawable_android_variablePadding 2
int styleable StateListDrawable_android_constantSize 3
int styleable StateListDrawable_android_enterFadeDuration 4
int styleable StateListDrawable_android_exitFadeDuration 5
int[] styleable StateListDrawableItem { 0x01010199 }
int styleable StateListDrawableItem_android_drawable 0
int[] styleable SwitchCompat { 0x01010124, 0x01010125, 0x01010142, 0x7f04011c, 0x7f04012b, 0x7f04013b, 0x7f04013c, 0x7f040140, 0x7f040152, 0x7f040153, 0x7f040154, 0x7f040169, 0x7f04016a, 0x7f04016b }
int styleable SwitchCompat_android_textOn 0
int styleable SwitchCompat_android_textOff 1
int styleable SwitchCompat_android_thumb 2
int styleable SwitchCompat_showText 3
int styleable SwitchCompat_splitTrack 4
int styleable SwitchCompat_switchMinWidth 5
int styleable SwitchCompat_switchPadding 6
int styleable SwitchCompat_switchTextAppearance 7
int styleable SwitchCompat_thumbTextPadding 8
int styleable SwitchCompat_thumbTint 9
int styleable SwitchCompat_thumbTintMode 10
int styleable SwitchCompat_track 11
int styleable SwitchCompat_trackTint 12
int styleable SwitchCompat_trackTintMode 13
int[] styleable SwitchPreference { 0x010101ef, 0x010101f0, 0x010101f1, 0x0101036b, 0x0101036c, 0x7f040076, 0x7f040139, 0x7f04013a, 0x7f040141, 0x7f040142 }
int styleable SwitchPreference_android_summaryOn 0
int styleable SwitchPreference_android_summaryOff 1
int styleable SwitchPreference_android_disableDependentsState 2
int styleable SwitchPreference_android_switchTextOn 3
int styleable SwitchPreference_android_switchTextOff 4
int styleable SwitchPreference_disableDependentsState 5
int styleable SwitchPreference_summaryOff 6
int styleable SwitchPreference_summaryOn 7
int styleable SwitchPreference_switchTextOff 8
int styleable SwitchPreference_switchTextOn 9
int[] styleable SwitchPreferenceCompat { 0x010101ef, 0x010101f0, 0x010101f1, 0x0101036b, 0x0101036c, 0x7f040076, 0x7f040139, 0x7f04013a, 0x7f040141, 0x7f040142 }
int styleable SwitchPreferenceCompat_android_summaryOn 0
int styleable SwitchPreferenceCompat_android_summaryOff 1
int styleable SwitchPreferenceCompat_android_disableDependentsState 2
int styleable SwitchPreferenceCompat_android_switchTextOn 3
int styleable SwitchPreferenceCompat_android_switchTextOff 4
int styleable SwitchPreferenceCompat_disableDependentsState 5
int styleable SwitchPreferenceCompat_summaryOff 6
int styleable SwitchPreferenceCompat_summaryOn 7
int styleable SwitchPreferenceCompat_switchTextOff 8
int styleable SwitchPreferenceCompat_switchTextOn 9
int[] styleable TextAppearance { 0x01010095, 0x01010096, 0x01010097, 0x01010098, 0x0101009a, 0x0101009b, 0x01010161, 0x01010162, 0x01010163, 0x01010164, 0x010103ac, 0x01010585, 0x7f04009d, 0x7f0400a6, 0x7f040144, 0x7f04014f }
int styleable TextAppearance_android_textSize 0
int styleable TextAppearance_android_typeface 1
int styleable TextAppearance_android_textStyle 2
int styleable TextAppearance_android_textColor 3
int styleable TextAppearance_android_textColorHint 4
int styleable TextAppearance_android_textColorLink 5
int styleable TextAppearance_android_shadowColor 6
int styleable TextAppearance_android_shadowDx 7
int styleable TextAppearance_android_shadowDy 8
int styleable TextAppearance_android_shadowRadius 9
int styleable TextAppearance_android_fontFamily 10
int styleable TextAppearance_android_textFontWeight 11
int styleable TextAppearance_fontFamily 12
int styleable TextAppearance_fontVariationSettings 13
int styleable TextAppearance_textAllCaps 14
int styleable TextAppearance_textLocale 15
int[] styleable Toolbar { 0x010100af, 0x01010140, 0x7f040046, 0x7f040053, 0x7f040054, 0x7f040062, 0x7f040063, 0x7f040064, 0x7f040065, 0x7f040066, 0x7f040067, 0x7f0400d7, 0x7f0400d8, 0x7f0400d9, 0x7f0400dd, 0x7f0400e0, 0x7f0400e1, 0x7f0400f3, 0x7f040133, 0x7f040134, 0x7f040135, 0x7f04015a, 0x7f04015b, 0x7f04015c, 0x7f04015d, 0x7f04015e, 0x7f04015f, 0x7f040160, 0x7f040161, 0x7f040162 }
int styleable Toolbar_android_gravity 0
int styleable Toolbar_android_minHeight 1
int styleable Toolbar_buttonGravity 2
int styleable Toolbar_collapseContentDescription 3
int styleable Toolbar_collapseIcon 4
int styleable Toolbar_contentInsetEnd 5
int styleable Toolbar_contentInsetEndWithActions 6
int styleable Toolbar_contentInsetLeft 7
int styleable Toolbar_contentInsetRight 8
int styleable Toolbar_contentInsetStart 9
int styleable Toolbar_contentInsetStartWithNavigation 10
int styleable Toolbar_logo 11
int styleable Toolbar_logoDescription 12
int styleable Toolbar_maxButtonHeight 13
int styleable Toolbar_menu 14
int styleable Toolbar_navigationContentDescription 15
int styleable Toolbar_navigationIcon 16
int styleable Toolbar_popupTheme 17
int styleable Toolbar_subtitle 18
int styleable Toolbar_subtitleTextAppearance 19
int styleable Toolbar_subtitleTextColor 20
int styleable Toolbar_title 21
int styleable Toolbar_titleMargin 22
int styleable Toolbar_titleMarginBottom 23
int styleable Toolbar_titleMarginEnd 24
int styleable Toolbar_titleMarginStart 25
int styleable Toolbar_titleMarginTop 26
int styleable Toolbar_titleMargins 27
int styleable Toolbar_titleTextAppearance 28
int styleable Toolbar_titleTextColor 29
int[] styleable View { 0x01010000, 0x010100da, 0x7f0400ea, 0x7f0400eb, 0x7f040150 }
int styleable View_android_theme 0
int styleable View_android_focusable 1
int styleable View_paddingEnd 2
int styleable View_paddingStart 3
int styleable View_theme 4
int[] styleable ViewBackgroundHelper { 0x010100d4, 0x7f04003c, 0x7f04003d }
int styleable ViewBackgroundHelper_android_background 0
int styleable ViewBackgroundHelper_backgroundTint 1
int styleable ViewBackgroundHelper_backgroundTintMode 2
int[] styleable ViewStubCompat { 0x010100d0, 0x010100f2, 0x010100f3 }
int styleable ViewStubCompat_android_id 0
int styleable ViewStubCompat_android_layout 1
int styleable ViewStubCompat_android_inflatedId 2
