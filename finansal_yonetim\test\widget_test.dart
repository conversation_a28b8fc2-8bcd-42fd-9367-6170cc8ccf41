// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';


import 'package:finansal_yonetim/services/security_service.dart';
import 'package:finansal_yonetim/services/database_service.dart';

void main() {
  testWidgets('App starts with PIN setup screen', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(
      MultiProvider(
        providers: [
          Provider<SecurityService>(create: (_) => SecurityService()),
          Provider<DatabaseService>(create: (_) => DatabaseService()),
        ],
        child: const MaterialApp(
          home: Scaffold(
            body: Center(
              child: Text('Test App'),
            ),
          ),
        ),
      ),
    );

    // Verify that the test app loads
    expect(find.text('Test App'), findsOneWidget);
  });

  group('Security Service Tests', () {
    test('PIN validation works correctly', () {
      // Test PIN format validation
      expect('123456'.length, equals(6));
      expect('12345'.length, lessThan(6));
      expect('1234567'.length, greaterThan(6));
    });
  });

  group('Database Service Tests', () {
    test('Database service initializes', () {
      final dbService = DatabaseService();
      expect(dbService, isNotNull);
    });
  });
}
