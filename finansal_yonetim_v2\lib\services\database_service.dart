import 'package:flutter/foundation.dart';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import '../models/bank_account.dart';
import '../models/crypto_asset.dart';
import '../models/transaction.dart';

class DatabaseService extends ChangeNotifier {
  static final DatabaseService _instance = DatabaseService._internal();
  factory DatabaseService() => _instance;
  DatabaseService._internal();

  static DatabaseService get instance => _instance;
  static Database? _database;

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    String path = join(await getDatabasesPath(), 'finansal_yonetim_pro.db');
    return await openDatabase(
      path,
      version: 1,
      onCreate: _onCreate,
    );
  }

  Future<void> _onCreate(Database db, int version) async {
    // Banka hesapları tablosu
    await db.execute('''
      CREATE TABLE bank_accounts (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        bank_name TEXT NOT NULL,
        account_type TEXT NOT NULL,
        account_number TEXT NOT NULL UNIQUE,
        iban TEXT,
        balance REAL NOT NULL DEFAULT 0.0,
        currency TEXT NOT NULL DEFAULT 'TRY',
        card_number TEXT,
        card_holder_name TEXT,
        expiry_date TEXT,
        cvv TEXT,
        is_active INTEGER NOT NULL DEFAULT 1,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL
      )
    ''');

    // Kripto varlıklar tablosu
    await db.execute('''
      CREATE TABLE crypto_assets (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        symbol TEXT NOT NULL,
        name TEXT NOT NULL,
        amount REAL NOT NULL DEFAULT 0.0,
        purchase_price REAL NOT NULL DEFAULT 0.0,
        current_price REAL NOT NULL DEFAULT 0.0,
        wallet_address TEXT,
        exchange_name TEXT,
        is_active INTEGER NOT NULL DEFAULT 1,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL
      )
    ''');

    // İşlemler tablosu
    await db.execute('''
      CREATE TABLE transactions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        type TEXT NOT NULL,
        category TEXT NOT NULL,
        amount REAL NOT NULL,
        description TEXT,
        account_id INTEGER,
        crypto_id INTEGER,
        date TEXT NOT NULL,
        created_at TEXT NOT NULL,
        FOREIGN KEY (account_id) REFERENCES bank_accounts (id),
        FOREIGN KEY (crypto_id) REFERENCES crypto_assets (id)
      )
    ''');

    // Kullanıcı ayarları tablosu
    await db.execute('''
      CREATE TABLE user_settings (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        key TEXT NOT NULL UNIQUE,
        value TEXT NOT NULL,
        updated_at TEXT NOT NULL
      )
    ''');

    // Varsayılan ayarları ekle
    await db.insert('user_settings', {
      'key': 'currency',
      'value': 'TRY',
      'updated_at': DateTime.now().toIso8601String(),
    });

    await db.insert('user_settings', {
      'key': 'theme_mode',
      'value': 'system',
      'updated_at': DateTime.now().toIso8601String(),
    });

    await db.insert('user_settings', {
      'key': 'biometric_enabled',
      'value': 'false',
      'updated_at': DateTime.now().toIso8601String(),
    });
  }

  // Banka hesapları CRUD işlemleri
  Future<int> insertBankAccount(BankAccount account) async {
    final db = await database;
    final id = await db.insert('bank_accounts', account.toMap());
    notifyListeners();
    return id;
  }

  Future<List<BankAccount>> getAllBankAccounts() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'bank_accounts',
      where: 'is_active = ?',
      whereArgs: [1],
      orderBy: 'created_at DESC',
    );
    return List.generate(maps.length, (i) => BankAccount.fromMap(maps[i]));
  }

  Future<BankAccount?> getBankAccount(int id) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'bank_accounts',
      where: 'id = ? AND is_active = ?',
      whereArgs: [id, 1],
    );
    if (maps.isNotEmpty) {
      return BankAccount.fromMap(maps.first);
    }
    return null;
  }

  Future<int> updateBankAccount(BankAccount account) async {
    final db = await database;
    final result = await db.update(
      'bank_accounts',
      account.toMap(),
      where: 'id = ?',
      whereArgs: [account.id],
    );
    notifyListeners();
    return result;
  }

  Future<int> deleteBankAccount(int id) async {
    final db = await database;
    final result = await db.update(
      'bank_accounts',
      {
        'is_active': 0,
        'updated_at': DateTime.now().toIso8601String(),
      },
      where: 'id = ?',
      whereArgs: [id],
    );
    notifyListeners();
    return result;
  }

  // Kripto varlıklar CRUD işlemleri
  Future<int> insertCryptoAsset(CryptoAsset asset) async {
    final db = await database;
    final id = await db.insert('crypto_assets', asset.toMap());
    notifyListeners();
    return id;
  }

  Future<List<CryptoAsset>> getAllCryptoAssets() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'crypto_assets',
      where: 'is_active = ?',
      whereArgs: [1],
      orderBy: 'created_at DESC',
    );
    return List.generate(maps.length, (i) => CryptoAsset.fromMap(maps[i]));
  }

  Future<CryptoAsset?> getCryptoAsset(int id) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'crypto_assets',
      where: 'id = ? AND is_active = ?',
      whereArgs: [id, 1],
    );
    if (maps.isNotEmpty) {
      return CryptoAsset.fromMap(maps.first);
    }
    return null;
  }

  Future<int> updateCryptoAsset(CryptoAsset asset) async {
    final db = await database;
    final result = await db.update(
      'crypto_assets',
      asset.toMap(),
      where: 'id = ?',
      whereArgs: [asset.id],
    );
    notifyListeners();
    return result;
  }

  Future<int> deleteCryptoAsset(int id) async {
    final db = await database;
    final result = await db.update(
      'crypto_assets',
      {
        'is_active': 0,
        'updated_at': DateTime.now().toIso8601String(),
      },
      where: 'id = ?',
      whereArgs: [id],
    );
    notifyListeners();
    return result;
  }

  // İşlemler CRUD işlemleri
  Future<int> insertTransaction(TransactionModel transaction) async {
    final db = await database;
    final id = await db.insert('transactions', transaction.toMap());
    notifyListeners();
    return id;
  }

  Future<List<TransactionModel>> getAllTransactions() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'transactions',
      orderBy: 'date DESC',
    );
    return List.generate(maps.length, (i) => TransactionModel.fromMap(maps[i]));
  }

  Future<List<TransactionModel>> getTransactionsByAccount(int accountId) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'transactions',
      where: 'account_id = ?',
      whereArgs: [accountId],
      orderBy: 'date DESC',
    );
    return List.generate(maps.length, (i) => TransactionModel.fromMap(maps[i]));
  }

  Future<List<TransactionModel>> getTransactionsByCrypto(int cryptoId) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'transactions',
      where: 'crypto_id = ?',
      whereArgs: [cryptoId],
      orderBy: 'date DESC',
    );
    return List.generate(maps.length, (i) => TransactionModel.fromMap(maps[i]));
  }

  // Toplam bakiye hesaplama
  Future<double> getTotalBalance() async {
    final accounts = await getAllBankAccounts();
    final cryptos = await getAllCryptoAssets();
    
    double bankTotal = accounts.fold(0.0, (sum, account) => sum + account.balance);
    double cryptoTotal = cryptos.fold(0.0, (sum, asset) => sum + (asset.amount * asset.currentPrice));
    
    return bankTotal + cryptoTotal;
  }

  // Ayarlar
  Future<String?> getSetting(String key) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'user_settings',
      where: 'key = ?',
      whereArgs: [key],
    );
    if (maps.isNotEmpty) {
      return maps.first['value'] as String;
    }
    return null;
  }

  Future<int> setSetting(String key, String value) async {
    final db = await database;
    final result = await db.insert(
      'user_settings',
      {
        'key': key,
        'value': value,
        'updated_at': DateTime.now().toIso8601String(),
      },
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
    notifyListeners();
    return result;
  }

  // Veritabanını temizle
  Future<void> clearDatabase() async {
    final db = await database;
    await db.delete('bank_accounts');
    await db.delete('crypto_assets');
    await db.delete('transactions');
    notifyListeners();
  }
}
