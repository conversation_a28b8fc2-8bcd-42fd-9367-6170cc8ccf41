import 'package:flutter/foundation.dart';
import 'package:dio/dio.dart';
import '../models/crypto_asset.dart';

class CryptoPrice {
  final String symbol;
  final String name;
  final double price;
  final double change24h;
  final double changePercentage24h;
  final String image;

  CryptoPrice({
    required this.symbol,
    required this.name,
    required this.price,
    required this.change24h,
    required this.changePercentage24h,
    required this.image,
  });

  factory CryptoPrice.fromJson(Map<String, dynamic> json) {
    return CryptoPrice(
      symbol: json['symbol']?.toString().toUpperCase() ?? '',
      name: json['name']?.toString() ?? '',
      price: (json['current_price'] ?? 0.0).toDouble(),
      change24h: (json['price_change_24h'] ?? 0.0).toDouble(),
      changePercentage24h: (json['price_change_percentage_24h'] ?? 0.0).toDouble(),
      image: json['image']?.toString() ?? '',
    );
  }
}

class CryptoApiService extends ChangeNotifier {
  final Dio _dio = Dio();
  final String _baseUrl = 'https://api.coingecko.com/api/v3';
  
  List<CryptoPrice> _cryptoPrices = [];
  bool _isLoading = false;
  String? _error;

  List<CryptoPrice> get cryptoPrices => _cryptoPrices;
  bool get isLoading => _isLoading;
  String? get error => _error;

  CryptoApiService() {
    _dio.options.baseUrl = _baseUrl;
    _dio.options.connectTimeout = const Duration(seconds: 10);
    _dio.options.receiveTimeout = const Duration(seconds: 10);
  }

  // Popüler kripto paralar listesi
  final List<String> _popularCryptos = [
    'bitcoin',
    'ethereum',
    'binancecoin',
    'cardano',
    'polkadot',
    'chainlink',
    'litecoin',
    'ripple',
    'dogecoin',
    'avalanche-2',
    'solana',
    'matic-network',
    'uniswap',
    'cosmos',
    'fantom',
  ];

  // Kripto fiyatlarını getir
  Future<void> fetchCryptoPrices() async {
    try {
      _isLoading = true;
      _error = null;
      notifyListeners();

      final response = await _dio.get(
        '/coins/markets',
        queryParameters: {
          'vs_currency': 'try', // Türk Lirası
          'ids': _popularCryptos.join(','),
          'order': 'market_cap_desc',
          'per_page': 50,
          'page': 1,
          'sparkline': false,
          'price_change_percentage': '24h',
        },
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = response.data;
        _cryptoPrices = data.map((json) => CryptoPrice.fromJson(json)).toList();
      } else {
        _error = 'Kripto fiyatları alınamadı';
      }
    } catch (e) {
      _error = 'Bağlantı hatası: ${e.toString()}';
      debugPrint('Crypto API Error: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Belirli bir kripto fiyatını getir
  Future<CryptoPrice?> getCryptoPrice(String symbol) async {
    try {
      // Önce cache'den kontrol et
      final cachedPrice = _cryptoPrices.firstWhere(
        (crypto) => crypto.symbol.toLowerCase() == symbol.toLowerCase(),
        orElse: () => CryptoPrice(
          symbol: '',
          name: '',
          price: 0.0,
          change24h: 0.0,
          changePercentage24h: 0.0,
          image: '',
        ),
      );

      if (cachedPrice.symbol.isNotEmpty) {
        return cachedPrice;
      }

      // Cache'de yoksa API'den getir
      final coinId = _getCoinId(symbol);
      if (coinId == null) return null;

      final response = await _dio.get(
        '/simple/price',
        queryParameters: {
          'ids': coinId,
          'vs_currencies': 'try',
          'include_24hr_change': true,
        },
      );

      if (response.statusCode == 200) {
        final data = response.data[coinId];
        if (data != null) {
          return CryptoPrice(
            symbol: symbol.toUpperCase(),
            name: _getCoinName(symbol),
            price: (data['try'] ?? 0.0).toDouble(),
            change24h: 0.0,
            changePercentage24h: (data['try_24h_change'] ?? 0.0).toDouble(),
            image: '',
          );
        }
      }
    } catch (e) {
      debugPrint('Get crypto price error: $e');
    }
    return null;
  }

  // Kripto varlıklarının fiyatlarını güncelle
  Future<void> updateCryptoAssetPrices(List<CryptoAsset> assets) async {
    try {
      for (final asset in assets) {
        final price = await getCryptoPrice(asset.symbol);
        if (price != null) {
          // Burada veritabanını güncelleme işlemi yapılabilir
          // DatabaseService.instance.updateCryptoAsset(
          //   asset.copyWith(currentPrice: price.price)
          // );
        }
      }
    } catch (e) {
      debugPrint('Update crypto asset prices error: $e');
    }
  }

  // Symbol'dan coin ID'sini getir
  String? _getCoinId(String symbol) {
    final symbolMap = {
      'BTC': 'bitcoin',
      'ETH': 'ethereum',
      'BNB': 'binancecoin',
      'ADA': 'cardano',
      'DOT': 'polkadot',
      'LINK': 'chainlink',
      'LTC': 'litecoin',
      'XRP': 'ripple',
      'DOGE': 'dogecoin',
      'AVAX': 'avalanche-2',
      'SOL': 'solana',
      'MATIC': 'matic-network',
      'UNI': 'uniswap',
      'ATOM': 'cosmos',
      'FTM': 'fantom',
    };
    return symbolMap[symbol.toUpperCase()];
  }

  // Symbol'dan coin adını getir
  String _getCoinName(String symbol) {
    final nameMap = {
      'BTC': 'Bitcoin',
      'ETH': 'Ethereum',
      'BNB': 'Binance Coin',
      'ADA': 'Cardano',
      'DOT': 'Polkadot',
      'LINK': 'Chainlink',
      'LTC': 'Litecoin',
      'XRP': 'XRP',
      'DOGE': 'Dogecoin',
      'AVAX': 'Avalanche',
      'SOL': 'Solana',
      'MATIC': 'Polygon',
      'UNI': 'Uniswap',
      'ATOM': 'Cosmos',
      'FTM': 'Fantom',
    };
    return nameMap[symbol.toUpperCase()] ?? symbol.toUpperCase();
  }

  // Kripto arama
  Future<List<CryptoPrice>> searchCryptos(String query) async {
    if (query.isEmpty) return _cryptoPrices;
    
    return _cryptoPrices.where((crypto) {
      return crypto.name.toLowerCase().contains(query.toLowerCase()) ||
             crypto.symbol.toLowerCase().contains(query.toLowerCase());
    }).toList();
  }

  // Trend kripto paralar
  List<CryptoPrice> getTrendingCryptos() {
    final sorted = List<CryptoPrice>.from(_cryptoPrices);
    sorted.sort((a, b) => b.changePercentage24h.compareTo(a.changePercentage24h));
    return sorted.take(10).toList();
  }

  // En çok düşenler
  List<CryptoPrice> getLosingCryptos() {
    final sorted = List<CryptoPrice>.from(_cryptoPrices);
    sorted.sort((a, b) => a.changePercentage24h.compareTo(b.changePercentage24h));
    return sorted.take(10).toList();
  }

  // Toplam piyasa değeri (basit hesaplama)
  double getTotalMarketCap() {
    return _cryptoPrices.fold(0.0, (sum, crypto) => sum + crypto.price);
  }

  // Cache'i temizle
  void clearCache() {
    _cryptoPrices.clear();
    _error = null;
    notifyListeners();
  }

  // Otomatik güncelleme başlat
  void startAutoUpdate() {
    // Her 5 dakikada bir güncelle
    Stream.periodic(const Duration(minutes: 5)).listen((_) {
      fetchCryptoPrices();
    });
  }
}
