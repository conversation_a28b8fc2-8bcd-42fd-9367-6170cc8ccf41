class BankAccount {
  final int? id;
  final String bankName;
  final String accountNumber;
  final String accountType;
  final double balance;
  final String currency;
  final DateTime createdAt;
  final DateTime updatedAt;

  BankAccount({
    this.id,
    required this.bankName,
    required this.accountNumber,
    required this.accountType,
    required this.balance,
    required this.currency,
    required this.createdAt,
    required this.updatedAt,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'bank_name': bankName,
      'account_number': accountNumber,
      'account_type': accountType,
      'balance': balance,
      'currency': currency,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  factory BankAccount.fromMap(Map<String, dynamic> map) {
    return BankAccount(
      id: map['id'],
      bankName: map['bank_name'],
      accountNumber: map['account_number'],
      accountType: map['account_type'],
      balance: map['balance'].toDouble(),
      currency: map['currency'],
      createdAt: DateTime.parse(map['created_at']),
      updatedAt: DateTime.parse(map['updated_at']),
    );
  }

  BankAccount copyWith({
    int? id,
    String? bankName,
    String? accountNumber,
    String? accountType,
    double? balance,
    String? currency,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return BankAccount(
      id: id ?? this.id,
      bankName: bankName ?? this.bankName,
      accountNumber: accountNumber ?? this.accountNumber,
      accountType: accountType ?? this.accountType,
      balance: balance ?? this.balance,
      currency: currency ?? this.currency,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}
