{"logs": [{"outputFile": "com.example.finansal.finansal_yonetim.app-mergeDebugResources-41:/values-as/values-as.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\a295c1332cd792405fffabf7b4bbac54\\transformed\\appcompat-1.2.0\\res\\values-as\\values-as.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,312,419,510,612,732,809,884,975,1068,1163,1257,1357,1450,1545,1639,1730,1821,1907,2020,2128,2227,2336,2452,2572,2739,2841", "endColumns": "107,98,106,90,101,119,76,74,90,92,94,93,99,92,94,93,90,90,85,112,107,98,108,115,119,166,101,82", "endOffsets": "208,307,414,505,607,727,804,879,970,1063,1158,1252,1352,1445,1540,1634,1725,1816,1902,2015,2123,2222,2331,2447,2567,2734,2836,2919"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,52", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,312,419,510,612,732,809,884,975,1068,1163,1257,1357,1450,1545,1639,1730,1821,1907,2020,2128,2227,2336,2452,2572,2739,5515", "endColumns": "107,98,106,90,101,119,76,74,90,92,94,93,99,92,94,93,90,90,85,112,107,98,108,115,119,166,101,82", "endOffsets": "208,307,414,505,607,727,804,879,970,1063,1158,1252,1352,1445,1540,1634,1725,1816,1902,2015,2123,2222,2331,2447,2567,2734,2836,5593"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\cceca150324ee75eadce8003b387b1fb\\transformed\\biometric-1.1.0\\res\\values-as\\values-as.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,168,259,383,504,633,766,892,1064,1170,1310,1452", "endColumns": "112,90,123,120,128,132,125,171,105,139,141,139", "endOffsets": "163,254,378,499,628,761,887,1059,1165,1305,1447,1587"}, "to": {"startLines": "36,38,40,41,42,43,44,45,46,47,48,49", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3591,3781,3956,4080,4201,4330,4463,4589,4761,4867,5007,5149", "endColumns": "112,90,123,120,128,132,125,171,105,139,141,139", "endOffsets": "3699,3867,4075,4196,4325,4458,4584,4756,4862,5002,5144,5284"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\b83b8b00b8346c9e7414a1f1298f055d\\transformed\\preference-1.2.1\\res\\values-as\\values-as.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,182,266,349,492,661,752", "endColumns": "76,83,82,142,168,90,79", "endOffsets": "177,261,344,487,656,747,827"}, "to": {"startLines": "37,39,50,51,54,55,56", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "3704,3872,5289,5372,5699,5868,5959", "endColumns": "76,83,82,142,168,90,79", "endOffsets": "3776,3951,5367,5510,5863,5954,6034"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\66aa7f682cf61ffe3ee75db6ee238d77\\transformed\\core-1.13.1\\res\\values-as\\values-as.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,156,259,367,472,576,676,805", "endColumns": "100,102,107,104,103,99,128,100", "endOffsets": "151,254,362,467,571,671,800,901"}, "to": {"startLines": "29,30,31,32,33,34,35,53", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2841,2942,3045,3153,3258,3362,3462,5598", "endColumns": "100,102,107,104,103,99,128,100", "endOffsets": "2937,3040,3148,3253,3357,3457,3586,5694"}}]}]}