import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../services/security_service.dart';
import '../../services/email_service.dart';
import '../../services/database_service.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  bool _biometricEnabled = false;
  bool _notificationsEnabled = true;
  String _email = '';
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final securityService = context.read<SecurityService>();
      final emailService = EmailService();
      
      final biometricEnabled = await securityService.isBiometricEnabled();
      final email = await emailService.getSavedEmail();
      
      setState(() {
        _biometricEnabled = biometricEnabled;
        _email = email ?? '';
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Ayarlar'),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : ListView(
              padding: const EdgeInsets.all(16),
              children: [
                _buildSecuritySection(),
                const SizedBox(height: 24),
                _buildAccountSection(),
                const SizedBox(height: 24),
                _buildDataSection(),
                const SizedBox(height: 24),
                _buildAboutSection(),
              ],
            ),
    );
  }

  Widget _buildSecuritySection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Güvenlik',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            // PIN değiştir
            ListTile(
              leading: const Icon(Icons.lock_outline),
              title: const Text('PIN Değiştir'),
              subtitle: const Text('Güvenlik PIN\'inizi değiştirin'),
              trailing: const Icon(Icons.chevron_right),
              onTap: _changePIN,
            ),
            
            // Biyometrik giriş
            FutureBuilder<bool>(
              future: context.read<SecurityService>().isBiometricAvailable(),
              builder: (context, snapshot) {
                if (snapshot.data == true) {
                  return SwitchListTile(
                    secondary: const Icon(Icons.fingerprint),
                    title: const Text('Biyometrik Giriş'),
                    subtitle: const Text('Parmak izi veya yüz tanıma ile giriş'),
                    value: _biometricEnabled,
                    onChanged: _toggleBiometric,
                  );
                }
                return const SizedBox.shrink();
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAccountSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Hesap',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            // E-posta
            ListTile(
              leading: const Icon(Icons.email_outlined),
              title: const Text('E-posta Adresi'),
              subtitle: Text(_email.isEmpty ? 'E-posta eklenmemiş' : _email),
              trailing: const Icon(Icons.chevron_right),
              onTap: _manageEmail,
            ),
            
            // Bildirimler
            SwitchListTile(
              secondary: const Icon(Icons.notifications_outlined),
              title: const Text('Bildirimler'),
              subtitle: const Text('Uygulama bildirimleri'),
              value: _notificationsEnabled,
              onChanged: (value) {
                setState(() {
                  _notificationsEnabled = value;
                });
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDataSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Veri Yönetimi',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            // Verileri dışa aktar
            ListTile(
              leading: const Icon(Icons.download_outlined),
              title: const Text('Verileri Dışa Aktar'),
              subtitle: const Text('Tüm verilerinizi JSON formatında indirin'),
              trailing: const Icon(Icons.chevron_right),
              onTap: _exportData,
            ),
            
            // Tüm verileri sil
            ListTile(
              leading: Icon(
                Icons.delete_forever_outlined,
                color: Theme.of(context).colorScheme.error,
              ),
              title: Text(
                'Tüm Verileri Sil',
                style: TextStyle(color: Theme.of(context).colorScheme.error),
              ),
              subtitle: const Text('Bu işlem geri alınamaz'),
              trailing: const Icon(Icons.chevron_right),
              onTap: _deleteAllData,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAboutSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Hakkında',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            // Uygulama versiyonu
            ListTile(
              leading: const Icon(Icons.info_outline),
              title: const Text('Versiyon'),
              subtitle: const Text('1.0.0'),
            ),
            
            // Gizlilik politikası
            ListTile(
              leading: const Icon(Icons.privacy_tip_outlined),
              title: const Text('Gizlilik Politikası'),
              trailing: const Icon(Icons.chevron_right),
              onTap: _showPrivacyPolicy,
            ),
            
            // Kullanım koşulları
            ListTile(
              leading: const Icon(Icons.description_outlined),
              title: const Text('Kullanım Koşulları'),
              trailing: const Icon(Icons.chevron_right),
              onTap: _showTermsOfService,
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _changePIN() async {
    // PIN değiştirme dialog'u
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('PIN Değiştir'),
        content: const Text(
          'PIN değiştirmek için önce mevcut PIN\'inizi girmeniz gerekir. '
          'Devam etmek istiyor musunuz?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('İptal'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // PIN değiştirme ekranına yönlendir
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('PIN değiştirme özelliği yakında eklenecek'),
                ),
              );
            },
            child: const Text('Devam'),
          ),
        ],
      ),
    );
  }

  Future<void> _toggleBiometric(bool value) async {
    try {
      final securityService = context.read<SecurityService>();

      if (value) {
        // Biyometrik kimlik doğrulama test et
        final isAuthenticated = await securityService.authenticateWithBiometrics();
        if (isAuthenticated) {
          await securityService.setBiometricEnabled(true);
          if (mounted) {
            setState(() {
              _biometricEnabled = true;
            });
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Biyometrik giriş etkinleştirildi')),
            );
          }
        }
      } else {
        await securityService.setBiometricEnabled(false);
        if (mounted) {
          setState(() {
            _biometricEnabled = false;
          });
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Biyometrik giriş devre dışı bırakıldı')),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Biyometrik ayar değiştirilirken hata oluştu')),
        );
      }
    }
  }

  void _manageEmail() {
    final emailController = TextEditingController(text: _email);
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('E-posta Yönetimi'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: emailController,
              keyboardType: TextInputType.emailAddress,
              decoration: const InputDecoration(
                labelText: 'E-posta Adresi',
                hintText: '<EMAIL>',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),
            Text(
              'E-posta adresiniz PIN sıfırlama için kullanılır',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
          ],
        ),
        actions: [
          if (_email.isNotEmpty)
            TextButton(
              onPressed: () async {
                final navigator = Navigator.of(context);
                final scaffoldMessenger = ScaffoldMessenger.of(context);
                final emailService = EmailService();
                await emailService.deleteEmail();
                if (mounted) {
                  setState(() {
                    _email = '';
                  });
                  navigator.pop();
                  scaffoldMessenger.showSnackBar(
                    const SnackBar(content: Text('E-posta adresi silindi')),
                  );
                }
              },
              child: Text(
                'Sil',
                style: TextStyle(color: Theme.of(context).colorScheme.error),
              ),
            ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('İptal'),
          ),
          ElevatedButton(
            onPressed: () async {
              final email = emailController.text.trim();
              if (email.isNotEmpty) {
                final navigator = Navigator.of(context);
                final scaffoldMessenger = ScaffoldMessenger.of(context);
                final emailService = EmailService();
                if (emailService.isValidEmail(email)) {
                  await emailService.saveEmail(email);
                  if (mounted) {
                    setState(() {
                      _email = email;
                    });
                    navigator.pop();
                    scaffoldMessenger.showSnackBar(
                      const SnackBar(content: Text('E-posta adresi kaydedildi')),
                    );
                  }
                } else {
                  if (mounted) {
                    scaffoldMessenger.showSnackBar(
                      const SnackBar(content: Text('Geçerli bir e-posta adresi girin')),
                    );
                  }
                }
              }
            },
            child: const Text('Kaydet'),
          ),
        ],
      ),
    );
  }

  void _exportData() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Veri dışa aktarma özelliği yakında eklenecek'),
      ),
    );
  }

  void _deleteAllData() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Tüm Verileri Sil'),
        content: const Text(
          'Bu işlem tüm hesaplarınızı, kripto varlıklarınızı ve ayarlarınızı silecek. '
          'Bu işlem geri alınamaz. Devam etmek istediğinizden emin misiniz?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('İptal'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await _performDataDeletion();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.error,
            ),
            child: const Text('Sil'),
          ),
        ],
      ),
    );
  }

  Future<void> _performDataDeletion() async {
    try {
      final dbService = context.read<DatabaseService>();
      final securityService = context.read<SecurityService>();
      final emailService = EmailService();

      // Tüm verileri sil
      final bankAccounts = await dbService.getAllBankAccounts();
      for (var account in bankAccounts) {
        if (account.id != null) {
          await dbService.deleteBankAccount(account.id!);
        }
      }

      final cryptoAssets = await dbService.getAllCryptoAssets();
      for (var asset in cryptoAssets) {
        if (asset.id != null) {
          await dbService.deleteCryptoAsset(asset.id!);
        }
      }

      // Güvenlik verilerini temizle
      await securityService.clearAllSecurityData();
      await emailService.deleteEmail();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Tüm veriler başarıyla silindi')),
        );

        // Uygulamayı yeniden başlat
        Navigator.of(context).pushNamedAndRemoveUntil('/', (route) => false);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Veriler silinirken hata oluştu')),
        );
      }
    }
  }

  void _showPrivacyPolicy() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Gizlilik Politikası'),
        content: const SingleChildScrollView(
          child: Text(
            'Bu uygulama tüm verilerinizi yerel olarak cihazınızda saklar. '
            'Hiçbir veri harici sunuculara gönderilmez. '
            'Verileriniz tamamen size aittir ve kontrolünüzdedir.\n\n'
            'Uygulama şu verileri toplar:\n'
            '• Banka hesap bilgileri\n'
            '• Kripto varlık bilgileri\n'
            '• Güvenlik PIN\'i (şifrelenmiş)\n'
            '• E-posta adresi (isteğe bağlı)\n\n'
            'Bu veriler sadece uygulamanın işlevselliği için kullanılır.',
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Kapat'),
          ),
        ],
      ),
    );
  }

  void _showTermsOfService() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Kullanım Koşulları'),
        content: const SingleChildScrollView(
          child: Text(
            'Bu uygulama kişisel finansal yönetim amaçlı geliştirilmiştir.\n\n'
            'Kullanım koşulları:\n'
            '• Uygulama "olduğu gibi" sunulmaktadır\n'
            '• Finansal kararlarınızdan siz sorumlusunuz\n'
            '• Verilerinizi düzenli olarak yedeklemeniz önerilir\n'
            '• Uygulama geliştiricisi veri kaybından sorumlu değildir\n\n'
            'Bu uygulamayı kullanarak bu koşulları kabul etmiş sayılırsınız.',
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Kapat'),
          ),
        ],
      ),
    );
  }
}
