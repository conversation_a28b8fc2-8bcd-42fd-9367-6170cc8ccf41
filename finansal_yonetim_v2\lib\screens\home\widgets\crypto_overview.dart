import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../theme/app_theme.dart';
import '../../../models/crypto_asset.dart';
import '../../../services/crypto_api_service.dart';

class CryptoOverview extends StatelessWidget {
  final List<CryptoAsset> cryptoAssets;

  const CryptoOverview({
    super.key,
    required this.cryptoAssets,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              '<PERSON><PERSON><PERSON> Genel Bakış',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.w700,
                color: Colors.grey.shade800,
              ),
            ),
            TextButton(
              onPressed: () {
                // TODO: <PERSON><PERSON><PERSON> say<PERSON>na git
              },
              child: Text(
                '<PERSON>ü<PERSON><PERSON><PERSON><PERSON>',
                style: TextStyle(
                  color: AppTheme.primaryColor,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        
        if (cryptoAssets.isEmpty)
          _buildEmptyState()
        else
          _buildCryptoList(),
      ],
    );
  }

  Widget _buildEmptyState() {
    return Container(
      padding: const EdgeInsets.all(32),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppTheme.cryptoGradient[0].withValues(alpha: 0.1),
            AppTheme.cryptoGradient[1].withValues(alpha: 0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: AppTheme.cryptoGradient[0].withValues(alpha: 0.2),
          width: 1.5,
        ),
      ),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: AppTheme.cryptoGradient,
              ),
              borderRadius: BorderRadius.circular(50),
            ),
            child: const Icon(
              Icons.currency_bitcoin,
              color: Colors.white,
              size: 32,
            ),
          ),
          const SizedBox(height: 16),
          Text(
            'Henüz kripto varlık yok',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Colors.grey.shade800,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'İlk kripto varlığınızı ekleyerek başlayın',
            style: TextStyle(
              color: Colors.grey.shade600,
              fontSize: 14,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildCryptoList() {
    return Consumer<CryptoApiService>(
      builder: (context, cryptoService, child) {
        return Column(
          children: [
            // Toplam değer kartı
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: AppTheme.cryptoGradient,
                ),
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: AppTheme.cryptoGradient[0].withValues(alpha: 0.3),
                    blurRadius: 15,
                    offset: const Offset(0, 8),
                  ),
                ],
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Toplam Kripto Değeri',
                        style: TextStyle(
                          color: Colors.white.withValues(alpha: 0.9),
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        '₺${_getTotalValue().toStringAsFixed(2)}',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 24,
                          fontWeight: FontWeight.w900,
                        ),
                      ),
                    ],
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          _getTotalProfitLoss() >= 0 
                              ? Icons.trending_up 
                              : Icons.trending_down,
                          color: Colors.white,
                          size: 16,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          '${_getTotalProfitLoss() >= 0 ? '+' : ''}${_getTotalProfitLoss().toStringAsFixed(1)}%',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Kripto listesi
            ...cryptoAssets.take(3).map((asset) => _buildCryptoItem(asset)),
            
            if (cryptoAssets.length > 3)
              Padding(
                padding: const EdgeInsets.only(top: 12),
                child: TextButton(
                  onPressed: () {
                    // TODO: Tüm kripto varlıkları sayfasına git
                  },
                  child: Text(
                    '+${cryptoAssets.length - 3} daha fazla',
                    style: TextStyle(
                      color: AppTheme.cryptoGradient[0],
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
          ],
        );
      },
    );
  }

  Widget _buildCryptoItem(CryptoAsset asset) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.grey.shade200,
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // Kripto ikonu
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: AppTheme.cryptoGradient,
              ),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              asset.cryptoIcon,
              style: const TextStyle(fontSize: 20),
            ),
          ),
          
          const SizedBox(width: 16),
          
          // Kripto bilgileri
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  asset.symbol,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w700,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '${asset.amount} ${asset.symbol}',
                  style: TextStyle(
                    color: Colors.grey.shade600,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
          
          // Değer ve kar/zarar
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                asset.formattedTotalValue,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w700,
                ),
              ),
              const SizedBox(height: 4),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: asset.isProfit 
                      ? Colors.green.withValues(alpha: 0.1)
                      : Colors.red.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  asset.formattedProfitLossPercentage,
                  style: TextStyle(
                    color: asset.isProfit ? Colors.green : Colors.red,
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  double _getTotalValue() {
    return cryptoAssets.fold(0.0, (sum, asset) => sum + asset.totalValue);
  }

  double _getTotalProfitLoss() {
    if (cryptoAssets.isEmpty) return 0.0;
    
    final totalProfitLoss = cryptoAssets.fold(0.0, (sum, asset) => sum + asset.profitLoss);
    final totalInvestment = cryptoAssets.fold(0.0, (sum, asset) => sum + (asset.amount * asset.purchasePrice));
    
    if (totalInvestment == 0) return 0.0;
    return (totalProfitLoss / totalInvestment) * 100;
  }
}
