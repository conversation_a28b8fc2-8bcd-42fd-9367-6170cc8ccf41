{"logs": [{"outputFile": "com.example.finansal.finansal_yonetim.app-mergeDebugResources-41:/values-mr/values-mr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\a295c1332cd792405fffabf7b4bbac54\\transformed\\appcompat-1.2.0\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,322,429,519,621,733,811,888,979,1072,1165,1262,1362,1455,1550,1644,1735,1826,1906,2013,2114,2210,2319,2421,2535,2692,2795", "endColumns": "110,105,106,89,101,111,77,76,90,92,92,96,99,92,94,93,90,90,79,106,100,95,108,101,113,156,102,79", "endOffsets": "211,317,424,514,616,728,806,883,974,1067,1160,1257,1357,1450,1545,1639,1730,1821,1901,2008,2109,2205,2314,2416,2530,2687,2790,2870"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,52", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,322,429,519,621,733,811,888,979,1072,1165,1262,1362,1455,1550,1644,1735,1826,1906,2013,2114,2210,2319,2421,2535,2692,5387", "endColumns": "110,105,106,89,101,111,77,76,90,92,92,96,99,92,94,93,90,90,79,106,100,95,108,101,113,156,102,79", "endOffsets": "211,317,424,514,616,728,806,883,974,1067,1160,1257,1357,1450,1545,1639,1730,1821,1901,2008,2109,2205,2314,2416,2530,2687,2790,5462"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\cceca150324ee75eadce8003b387b1fb\\transformed\\biometric-1.1.0\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,162,249,371,500,629,763,895,1029,1125,1269,1414", "endColumns": "106,86,121,128,128,133,131,133,95,143,144,125", "endOffsets": "157,244,366,495,624,758,890,1024,1120,1264,1409,1535"}, "to": {"startLines": "36,38,40,41,42,43,44,45,46,47,48,49", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3527,3706,3877,3999,4128,4257,4391,4523,4657,4753,4897,5042", "endColumns": "106,86,121,128,128,133,131,133,95,143,144,125", "endOffsets": "3629,3788,3994,4123,4252,4386,4518,4652,4748,4892,5037,5163"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\b83b8b00b8346c9e7414a1f1298f055d\\transformed\\preference-1.2.1\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,177,261,343,480,649,729", "endColumns": "71,83,81,136,168,79,77", "endOffsets": "172,256,338,475,644,724,802"}, "to": {"startLines": "37,39,50,51,54,55,56", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "3634,3793,5168,5250,5568,5737,5817", "endColumns": "71,83,81,136,168,79,77", "endOffsets": "3701,3872,5245,5382,5732,5812,5890"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\66aa7f682cf61ffe3ee75db6ee238d77\\transformed\\core-1.13.1\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,259,360,463,565,670,787", "endColumns": "99,103,100,102,101,104,116,100", "endOffsets": "150,254,355,458,560,665,782,883"}, "to": {"startLines": "29,30,31,32,33,34,35,53", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2795,2895,2999,3100,3203,3305,3410,5467", "endColumns": "99,103,100,102,101,104,116,100", "endOffsets": "2890,2994,3095,3198,3300,3405,3522,5563"}}]}]}