class CryptoAsset {
  final int? id;
  final String symbol;
  final String name;
  final double amount;
  final double purchasePrice;
  final double currentPrice;
  final DateTime purchaseDate;
  final DateTime updatedAt;

  CryptoAsset({
    this.id,
    required this.symbol,
    required this.name,
    required this.amount,
    required this.purchasePrice,
    required this.currentPrice,
    required this.purchaseDate,
    required this.updatedAt,
  });

  double get totalValue => amount * currentPrice;
  double get totalInvestment => amount * purchasePrice;
  double get profitLoss => totalValue - totalInvestment;
  double get profitLossPercentage => 
      totalInvestment > 0 ? (profitLoss / totalInvestment) * 100 : 0;

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'symbol': symbol,
      'name': name,
      'amount': amount,
      'purchase_price': purchasePrice,
      'current_price': currentPrice,
      'purchase_date': purchaseDate.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  factory CryptoAsset.fromMap(Map<String, dynamic> map) {
    return CryptoAsset(
      id: map['id'],
      symbol: map['symbol'],
      name: map['name'],
      amount: map['amount'].toDouble(),
      purchasePrice: map['purchase_price'].toDouble(),
      currentPrice: map['current_price'].toDouble(),
      purchaseDate: DateTime.parse(map['purchase_date']),
      updatedAt: DateTime.parse(map['updated_at']),
    );
  }

  CryptoAsset copyWith({
    int? id,
    String? symbol,
    String? name,
    double? amount,
    double? purchasePrice,
    double? currentPrice,
    DateTime? purchaseDate,
    DateTime? updatedAt,
  }) {
    return CryptoAsset(
      id: id ?? this.id,
      symbol: symbol ?? this.symbol,
      name: name ?? this.name,
      amount: amount ?? this.amount,
      purchasePrice: purchasePrice ?? this.purchasePrice,
      currentPrice: currentPrice ?? this.currentPrice,
      purchaseDate: purchaseDate ?? this.purchaseDate,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}
