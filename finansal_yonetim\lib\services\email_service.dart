import 'dart:math';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
// import 'package:mailer/mailer.dart';
// import 'package:mailer/smtp_server.dart';

class EmailService {
  static final EmailService _instance = EmailService._internal();
  factory EmailService() => _instance;
  EmailService._internal();

  static const _storage = FlutterSecureStorage();
  static const String _resetCodeKey = 'reset_code';
  static const String _resetCodeTimeKey = 'reset_code_time';
  static const String _userEmailKey = 'user_email';

  // E-posta adresini kaydet
  Future<bool> saveEmail(String email) async {
    try {
      await _storage.write(key: _userEmailKey, value: email);
      return true;
    } catch (e) {
      return false;
    }
  }

  // Kayıtlı e-posta adresini al
  Future<String?> getSavedEmail() async {
    return await _storage.read(key: _userEmailKey);
  }

  // E-posta adresini sil
  Future<bool> deleteEmail() async {
    try {
      await _storage.delete(key: _userEmailKey);
      return true;
    } catch (e) {
      return false;
    }
  }

  // PIN sıfırlama kodu oluştur ve gönder
  Future<bool> sendResetCode(String email) async {
    try {
      // 6 haneli rastgele kod oluştur
      final resetCode = _generateResetCode();
      
      // Kodu ve zamanı güvenli depolamaya kaydet
      await _storage.write(key: _resetCodeKey, value: resetCode);
      await _storage.write(key: _resetCodeTimeKey, value: DateTime.now().toIso8601String());
      
      // E-posta gönder (demo amaçlı - gerçek uygulamada SMTP ayarları gerekli)
      final success = await _sendEmail(email, resetCode);
      
      return success;
    } catch (e) {
      return false;
    }
  }

  // Sıfırlama kodunu doğrula
  Future<bool> verifyResetCode(String code) async {
    try {
      final storedCode = await _storage.read(key: _resetCodeKey);
      final storedTime = await _storage.read(key: _resetCodeTimeKey);
      
      if (storedCode == null || storedTime == null) {
        return false;
      }
      
      // Kod 10 dakika geçerli
      final codeTime = DateTime.parse(storedTime);
      final now = DateTime.now();
      final difference = now.difference(codeTime).inMinutes;
      
      if (difference > 10) {
        // Süresi dolmuş kodu temizle
        await _clearResetCode();
        return false;
      }
      
      return storedCode == code;
    } catch (e) {
      return false;
    }
  }

  // Sıfırlama kodunu temizle
  Future<void> _clearResetCode() async {
    await _storage.delete(key: _resetCodeKey);
    await _storage.delete(key: _resetCodeTimeKey);
  }

  // 6 haneli rastgele kod oluştur
  String _generateResetCode() {
    final random = Random();
    return (100000 + random.nextInt(900000)).toString();
  }

  // E-posta gönder (demo implementasyonu)
  Future<bool> _sendEmail(String email, String resetCode) async {
    try {
      // Demo amaçlı - gerçek uygulamada SMTP sunucu ayarları gerekli
      // Bu örnekte sadece konsola yazdırıyoruz
      // print('E-posta gönderildi: $email');
      // print('Sıfırlama kodu: $resetCode');
      
      // Gerçek implementasyon için aşağıdaki kodu kullanabilirsiniz:
      /*
      final smtpServer = gmail('<EMAIL>', 'your-app-password');
      
      final message = Message()
        ..from = Address('<EMAIL>', 'Finansal Yönetim')
        ..recipients.add(email)
        ..subject = 'PIN Sıfırlama Kodu'
        ..text = 'PIN sıfırlama kodunuz: $resetCode\n\nBu kod 10 dakika geçerlidir.';
      
      final sendReport = await send(message, smtpServer);
      return sendReport.sent.isNotEmpty;
      */
      
      return true; // Demo için her zaman başarılı
    } catch (e) {
      // print('E-posta gönderme hatası: $e');
      return false;
    }
  }

  // E-posta doğrulama
  bool isValidEmail(String email) {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
  }

  // Sıfırlama işlemi tamamlandıktan sonra temizlik
  Future<void> completeReset() async {
    await _clearResetCode();
  }

  // E-posta kayıtlı mı kontrol et
  Future<bool> isEmailRegistered() async {
    final email = await getSavedEmail();
    return email != null && email.isNotEmpty;
  }

  // Son gönderilen kodun zamanını al
  Future<DateTime?> getLastCodeTime() async {
    final timeString = await _storage.read(key: _resetCodeTimeKey);
    if (timeString != null) {
      return DateTime.parse(timeString);
    }
    return null;
  }

  // Yeni kod göndermek için bekleme süresi
  Future<int> getRemainingWaitTime() async {
    final lastTime = await getLastCodeTime();
    if (lastTime == null) return 0;
    
    final now = DateTime.now();
    final difference = now.difference(lastTime).inSeconds;
    const waitTime = 60; // 1 dakika bekleme
    
    return waitTime - difference > 0 ? waitTime - difference : 0;
  }
}
