import 'dart:convert';
import 'package:http/http.dart' as http;

class CryptoService {
  static final CryptoService _instance = CryptoService._internal();
  factory CryptoService() => _instance;
  CryptoService._internal();

  // CoinGecko API (ücretsiz, API key gerektirmez)
  static const String _baseUrl = 'https://api.coingecko.com/api/v3';
  
  // Popüler kripto paraların listesi
  static const List<Map<String, String>> popularCryptos = [
    {'id': 'bitcoin', 'symbol': 'BTC', 'name': 'Bitcoin'},
    {'id': 'ethereum', 'symbol': 'ETH', 'name': 'Ethereum'},
    {'id': 'binancecoin', 'symbol': 'BNB', 'name': 'BNB'},
    {'id': 'cardano', 'symbol': 'ADA', 'name': 'Cardano'},
    {'id': 'solana', 'symbol': 'SOL', 'name': '<PERSON><PERSON>'},
    {'id': 'ripple', 'symbol': 'XRP', 'name': 'XRP'},
    {'id': 'dogecoin', 'symbol': 'DOGE', 'name': '<PERSON><PERSON><PERSON><PERSON>'},
    {'id': 'polygon', 'symbol': 'MATIC', 'name': 'Polygon'},
    {'id': 'chainlink', 'symbol': 'LINK', 'name': 'Chainlink'},
    {'id': 'litecoin', 'symbol': 'LTC', 'name': 'Litecoin'},
  ];

  // Tek bir kripto paranın güncel fiyatını al
  Future<double?> getCryptoPrice(String cryptoId) async {
    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/simple/price?ids=$cryptoId&vs_currencies=usd'),
        headers: {'Accept': 'application/json'},
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return data[cryptoId]['usd']?.toDouble();
      }
      return null;
    } catch (e) {
      print('Kripto fiyat alma hatası: $e');
      return null;
    }
  }

  // Birden fazla kripto paranın fiyatlarını al
  Future<Map<String, double>> getMultipleCryptoPrices(List<String> cryptoIds) async {
    try {
      final ids = cryptoIds.join(',');
      final response = await http.get(
        Uri.parse('$_baseUrl/simple/price?ids=$ids&vs_currencies=usd'),
        headers: {'Accept': 'application/json'},
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        Map<String, double> prices = {};
        
        for (String id in cryptoIds) {
          if (data[id] != null && data[id]['usd'] != null) {
            prices[id] = data[id]['usd'].toDouble();
          }
        }
        
        return prices;
      }
      return {};
    } catch (e) {
      print('Çoklu kripto fiyat alma hatası: $e');
      return {};
    }
  }

  // Kripto para detaylı bilgilerini al
  Future<Map<String, dynamic>?> getCryptoDetails(String cryptoId) async {
    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/coins/$cryptoId?localization=false&tickers=false&market_data=true&community_data=false&developer_data=false'),
        headers: {'Accept': 'application/json'},
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return {
          'id': data['id'],
          'symbol': data['symbol']?.toString().toUpperCase(),
          'name': data['name'],
          'current_price': data['market_data']['current_price']['usd']?.toDouble(),
          'price_change_24h': data['market_data']['price_change_24h']?.toDouble(),
          'price_change_percentage_24h': data['market_data']['price_change_percentage_24h']?.toDouble(),
          'market_cap': data['market_data']['market_cap']['usd']?.toDouble(),
          'total_volume': data['market_data']['total_volume']['usd']?.toDouble(),
          'image': data['image']['small'],
        };
      }
      return null;
    } catch (e) {
      print('Kripto detay alma hatası: $e');
      return null;
    }
  }

  // Kripto para arama
  Future<List<Map<String, dynamic>>> searchCrypto(String query) async {
    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/search?query=$query'),
        headers: {'Accept': 'application/json'},
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final coins = data['coins'] as List;
        
        return coins.take(10).map((coin) => {
          'id': coin['id'],
          'symbol': coin['symbol']?.toString().toUpperCase(),
          'name': coin['name'],
          'image': coin['thumb'],
        }).toList();
      }
      return [];
    } catch (e) {
      print('Kripto arama hatası: $e');
      return [];
    }
  }

  // Popüler kripto paraların fiyatlarını al
  Future<List<Map<String, dynamic>>> getPopularCryptos() async {
    try {
      final ids = popularCryptos.map((crypto) => crypto['id']!).join(',');
      final response = await http.get(
        Uri.parse('$_baseUrl/simple/price?ids=$ids&vs_currencies=usd&include_24hr_change=true'),
        headers: {'Accept': 'application/json'},
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        List<Map<String, dynamic>> result = [];
        
        for (var crypto in popularCryptos) {
          final id = crypto['id']!;
          if (data[id] != null) {
            result.add({
              'id': id,
              'symbol': crypto['symbol'],
              'name': crypto['name'],
              'current_price': data[id]['usd']?.toDouble() ?? 0.0,
              'price_change_percentage_24h': data[id]['usd_24h_change']?.toDouble() ?? 0.0,
            });
          }
        }
        
        return result;
      }
      return [];
    } catch (e) {
      print('Popüler kripto alma hatası: $e');
      return [];
    }
  }

  // Kripto para geçmiş fiyat verilerini al (basit versiyon)
  Future<List<Map<String, dynamic>>> getCryptoPriceHistory(String cryptoId, int days) async {
    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/coins/$cryptoId/market_chart?vs_currency=usd&days=$days'),
        headers: {'Accept': 'application/json'},
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final prices = data['prices'] as List;
        
        return prices.map((price) => {
          'timestamp': price[0],
          'price': price[1].toDouble(),
          'date': DateTime.fromMillisecondsSinceEpoch(price[0]),
        }).toList();
      }
      return [];
    } catch (e) {
      print('Kripto geçmiş fiyat alma hatası: $e');
      return [];
    }
  }

  // USD'den TRY'ye çevrim oranını al
  Future<double?> getUsdToTryRate() async {
    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/simple/price?ids=tether&vs_currencies=try'),
        headers: {'Accept': 'application/json'},
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return data['tether']['try']?.toDouble();
      }
      return null;
    } catch (e) {
      print('USD/TRY kuru alma hatası: $e');
      return null;
    }
  }

  // Kripto ID'sini symbol'den bul
  String? getCryptoIdBySymbol(String symbol) {
    final crypto = popularCryptos.firstWhere(
      (crypto) => crypto['symbol']?.toLowerCase() == symbol.toLowerCase(),
      orElse: () => {},
    );
    return crypto['id'];
  }

  // Symbol'den kripto adını bul
  String? getCryptoNameBySymbol(String symbol) {
    final crypto = popularCryptos.firstWhere(
      (crypto) => crypto['symbol']?.toLowerCase() == symbol.toLowerCase(),
      orElse: () => {},
    );
    return crypto['name'];
  }
}
