class CryptoAsset {
  final int? id;
  final String symbol;
  final String name;
  final double amount;
  final double purchasePrice;
  final double currentPrice;
  final String? walletAddress;
  final String? exchangeName;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;

  CryptoAsset({
    this.id,
    required this.symbol,
    required this.name,
    this.amount = 0.0,
    this.purchasePrice = 0.0,
    this.currentPrice = 0.0,
    this.walletAddress,
    this.exchangeName,
    this.isActive = true,
    DateTime? createdAt,
    DateTime? updatedAt,
  })  : createdAt = createdAt ?? DateTime.now(),
        updatedAt = updatedAt ?? DateTime.now();

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'symbol': symbol,
      'name': name,
      'amount': amount,
      'purchase_price': purchasePrice,
      'current_price': currentPrice,
      'wallet_address': walletAddress,
      'exchange_name': exchangeName,
      'is_active': isActive ? 1 : 0,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  factory CryptoAsset.fromMap(Map<String, dynamic> map) {
    return CryptoAsset(
      id: map['id']?.toInt(),
      symbol: map['symbol'] ?? '',
      name: map['name'] ?? '',
      amount: map['amount']?.toDouble() ?? 0.0,
      purchasePrice: map['purchase_price']?.toDouble() ?? 0.0,
      currentPrice: map['current_price']?.toDouble() ?? 0.0,
      walletAddress: map['wallet_address'],
      exchangeName: map['exchange_name'],
      isActive: (map['is_active'] ?? 1) == 1,
      createdAt: DateTime.parse(map['created_at']),
      updatedAt: DateTime.parse(map['updated_at']),
    );
  }

  CryptoAsset copyWith({
    int? id,
    String? symbol,
    String? name,
    double? amount,
    double? purchasePrice,
    double? currentPrice,
    String? walletAddress,
    String? exchangeName,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return CryptoAsset(
      id: id ?? this.id,
      symbol: symbol ?? this.symbol,
      name: name ?? this.name,
      amount: amount ?? this.amount,
      purchasePrice: purchasePrice ?? this.purchasePrice,
      currentPrice: currentPrice ?? this.currentPrice,
      walletAddress: walletAddress ?? this.walletAddress,
      exchangeName: exchangeName ?? this.exchangeName,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? DateTime.now(),
    );
  }

  @override
  String toString() {
    return 'CryptoAsset(id: $id, symbol: $symbol, name: $name, amount: $amount, currentPrice: $currentPrice)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CryptoAsset &&
        other.id == id &&
        other.symbol == symbol &&
        other.name == name &&
        other.amount == amount &&
        other.purchasePrice == purchasePrice &&
        other.currentPrice == currentPrice &&
        other.walletAddress == walletAddress &&
        other.exchangeName == exchangeName &&
        other.isActive == isActive;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        symbol.hashCode ^
        name.hashCode ^
        amount.hashCode ^
        purchasePrice.hashCode ^
        currentPrice.hashCode ^
        walletAddress.hashCode ^
        exchangeName.hashCode ^
        isActive.hashCode;
  }

  // Toplam değer
  double get totalValue {
    return amount * currentPrice;
  }

  // Kar/Zarar
  double get profitLoss {
    return totalValue - (amount * purchasePrice);
  }

  // Kar/Zarar yüzdesi
  double get profitLossPercentage {
    if (purchasePrice == 0) return 0.0;
    return ((currentPrice - purchasePrice) / purchasePrice) * 100;
  }

  // Kar/Zarar durumu
  bool get isProfit {
    return profitLoss > 0;
  }

  // Formatlanmış toplam değer
  String get formattedTotalValue {
    return '₺${totalValue.toStringAsFixed(2)}';
  }

  // Formatlanmış kar/zarar
  String get formattedProfitLoss {
    final prefix = isProfit ? '+' : '';
    return '$prefix₺${profitLoss.toStringAsFixed(2)}';
  }

  // Formatlanmış kar/zarar yüzdesi
  String get formattedProfitLossPercentage {
    final prefix = isProfit ? '+' : '';
    return '$prefix${profitLossPercentage.toStringAsFixed(2)}%';
  }

  // Wallet adresini maskele
  String get maskedWalletAddress {
    if (walletAddress == null || walletAddress!.length < 8) return '****';
    return '${walletAddress!.substring(0, 4)}...${walletAddress!.substring(walletAddress!.length - 4)}';
  }

  // Kripto ikonu
  String get cryptoIcon {
    switch (symbol.toUpperCase()) {
      case 'BTC':
        return '₿';
      case 'ETH':
        return 'Ξ';
      case 'BNB':
        return '🟡';
      case 'ADA':
        return '🔵';
      case 'DOT':
        return '🔴';
      case 'LINK':
        return '🔗';
      case 'LTC':
        return '🥈';
      case 'XRP':
        return '💧';
      case 'DOGE':
        return '🐕';
      case 'AVAX':
        return '🏔️';
      case 'SOL':
        return '☀️';
      case 'MATIC':
        return '🟣';
      case 'UNI':
        return '🦄';
      case 'ATOM':
        return '⚛️';
      case 'FTM':
        return '👻';
      default:
        return '🪙';
    }
  }

  // Kripto rengi
  String get cryptoColor {
    switch (symbol.toUpperCase()) {
      case 'BTC':
        return '#F7931A';
      case 'ETH':
        return '#627EEA';
      case 'BNB':
        return '#F3BA2F';
      case 'ADA':
        return '#0033AD';
      case 'DOT':
        return '#E6007A';
      case 'LINK':
        return '#375BD2';
      case 'LTC':
        return '#BFBBBB';
      case 'XRP':
        return '#23292F';
      case 'DOGE':
        return '#C2A633';
      case 'AVAX':
        return '#E84142';
      case 'SOL':
        return '#9945FF';
      case 'MATIC':
        return '#8247E5';
      case 'UNI':
        return '#FF007A';
      case 'ATOM':
        return '#2E3148';
      case 'FTM':
        return '#13B5EC';
      default:
        return '#6B7280';
    }
  }

  // Popüler kripto mu?
  bool get isPopular {
    const popularCryptos = [
      'BTC', 'ETH', 'BNB', 'ADA', 'DOT', 'LINK', 'LTC', 'XRP', 'DOGE',
      'AVAX', 'SOL', 'MATIC', 'UNI', 'ATOM', 'FTM'
    ];
    return popularCryptos.contains(symbol.toUpperCase());
  }

  // Risk seviyesi
  String get riskLevel {
    switch (symbol.toUpperCase()) {
      case 'BTC':
      case 'ETH':
        return 'Düşük';
      case 'BNB':
      case 'ADA':
      case 'DOT':
      case 'LINK':
      case 'LTC':
        return 'Orta';
      case 'XRP':
      case 'AVAX':
      case 'SOL':
      case 'MATIC':
      case 'UNI':
      case 'ATOM':
        return 'Orta-Yüksek';
      case 'DOGE':
      case 'FTM':
        return 'Yüksek';
      default:
        return 'Çok Yüksek';
    }
  }
}
