import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../services/ai_advisor_service.dart';
import '../../services/database_service.dart';

class AdvisorScreen extends StatefulWidget {
  const AdvisorScreen({super.key});

  @override
  State<AdvisorScreen> createState() => _AdvisorScreenState();
}

class _AdvisorScreenState extends State<AdvisorScreen> {
  List<FinancialAdvice> _advice = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadAdvice();
  }

  Future<void> _loadAdvice() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final advisorService = AIAdvisorService();
      final advice = await advisorService.getFinancialAdvice();
      
      setState(() {
        _advice = advice;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surfaceContainerLowest,
      appBar: AppBar(
        title: const Text(
          'AI Finansal Danışman',
          style: TextStyle(fontWeight: FontWeight.w600),
        ),
        backgroundColor: Theme.of(context).colorScheme.surface,
        elevation: 0,
        actions: [
          IconButton(
            onPressed: _loadAdvice,
            icon: const Icon(Icons.refresh),
            tooltip: 'Tavsiyeleri Yenile',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              onRefresh: _loadAdvice,
              child: _buildAdviceList(),
            ),
    );
  }

  Widget _buildAdviceList() {
    if (_advice.isEmpty) {
      return _buildEmptyState();
    }

    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        // Günlük ipucu
        _buildDailyTip(),
        const SizedBox(height: 24),
        
        // Öneriler başlığı
        Text(
          'Kişiselleştirilmiş Öneriler',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        
        // Öneri kartları
        ..._advice.map((advice) => _buildAdviceCard(advice)),
        
        const SizedBox(height: 24),
        
        // Finansal sağlık skoru
        _buildFinancialHealthScore(),
      ],
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.psychology_outlined,
            size: 80,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
          const SizedBox(height: 16),
          Text(
            'Henüz öneri yok',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 8),
          Text(
            'Hesap ve kripto varlık ekleyerek kişiselleştirilmiş öneriler alın',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: _loadAdvice,
            icon: const Icon(Icons.refresh),
            label: const Text('Yenile'),
          ),
        ],
      ),
    );
  }

  Widget _buildDailyTip() {
    final dailyTip = AIAdvisorService().getDailyTip();
    
    return Card(
      elevation: 4,
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              Theme.of(context).colorScheme.primaryContainer,
              Theme.of(context).colorScheme.secondaryContainer,
            ],
          ),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    Icons.lightbulb,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Günün İpucu',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).colorScheme.onPrimaryContainer,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Text(
                dailyTip.description,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Theme.of(context).colorScheme.onPrimaryContainer,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAdviceCard(FinancialAdvice advice) {
    IconData icon;
    Color iconColor;
    Color backgroundColor;

    switch (advice.type) {
      case AdviceType.warning:
        icon = Icons.warning_outlined;
        iconColor = Theme.of(context).colorScheme.error;
        backgroundColor = Theme.of(context).colorScheme.errorContainer;
        break;
      case AdviceType.suggestion:
        icon = Icons.lightbulb_outline;
        iconColor = Theme.of(context).colorScheme.primary;
        backgroundColor = Theme.of(context).colorScheme.primaryContainer;
        break;
      case AdviceType.tip:
        icon = Icons.tips_and_updates_outlined;
        iconColor = Theme.of(context).colorScheme.tertiary;
        backgroundColor = Theme.of(context).colorScheme.tertiaryContainer;
        break;
      case AdviceType.alert:
        icon = Icons.notification_important_outlined;
        iconColor = Theme.of(context).colorScheme.error;
        backgroundColor = Theme.of(context).colorScheme.errorContainer;
        break;
    }

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Container(
        decoration: BoxDecoration(
          color: backgroundColor.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: iconColor.withValues(alpha: 0.3),
            width: 1,
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(icon, color: iconColor),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      advice.title,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  _buildPriorityChip(advice.priority),
                ],
              ),
              const SizedBox(height: 12),
              Text(
                advice.description,
                style: Theme.of(context).textTheme.bodyMedium,
              ),
              const SizedBox(height: 8),
              Text(
                _formatDate(advice.createdAt),
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPriorityChip(AdvicePriority priority) {
    Color chipColor;
    String label;

    switch (priority) {
      case AdvicePriority.high:
        chipColor = Theme.of(context).colorScheme.error;
        label = 'Yüksek';
        break;
      case AdvicePriority.medium:
        chipColor = Theme.of(context).colorScheme.primary;
        label = 'Orta';
        break;
      case AdvicePriority.low:
        chipColor = Theme.of(context).colorScheme.tertiary;
        label = 'Düşük';
        break;
    }

    return Chip(
      label: Text(
        label,
        style: TextStyle(
          color: chipColor,
          fontSize: 12,
          fontWeight: FontWeight.bold,
        ),
      ),
      backgroundColor: chipColor.withValues(alpha: 0.1),
      side: BorderSide(color: chipColor.withValues(alpha: 0.3)),
      padding: const EdgeInsets.symmetric(horizontal: 8),
    );
  }

  Widget _buildFinancialHealthScore() {
    return FutureBuilder<double>(
      future: _calculateFinancialHealthScore(),
      builder: (context, snapshot) {
        if (!snapshot.hasData) {
          return const SizedBox.shrink();
        }

        final score = snapshot.data!;
        final scoreColor = _getScoreColor(score);

        return Card(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Finansal Sağlık Skoru',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: LinearProgressIndicator(
                        value: score / 100,
                        backgroundColor: Theme.of(context).colorScheme.surfaceContainerHighest,
                        valueColor: AlwaysStoppedAnimation<Color>(scoreColor),
                        minHeight: 8,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Text(
                      '${score.toInt()}/100',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: scoreColor,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Text(
                  _getScoreDescription(score),
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Future<double> _calculateFinancialHealthScore() async {
    try {
      final dbService = context.read<DatabaseService>();
      final bankAccounts = await dbService.getAllBankAccounts();
      final cryptoAssets = await dbService.getAllCryptoAssets();
      
      double score = 0;
      
      // Hesap çeşitliliği (20 puan)
      if (bankAccounts.isNotEmpty) score += 10;
      if (bankAccounts.length > 1) score += 10;
      
      // Kripto çeşitliliği (20 puan)
      if (cryptoAssets.isNotEmpty) score += 10;
      if (cryptoAssets.length > 1) score += 10;
      
      // Toplam bakiye (30 puan)
      final totalBalance = await dbService.getTotalBalance();
      if (totalBalance > 1000) score += 10;
      if (totalBalance > 10000) score += 10;
      if (totalBalance > 50000) score += 10;
      
      // Risk dağılımı (30 puan)
      if (bankAccounts.isNotEmpty && cryptoAssets.isNotEmpty) {
        final totalBank = bankAccounts.fold(0.0, (sum, account) => sum + account.balance);
        final totalCrypto = cryptoAssets.fold(0.0, (sum, asset) => sum + asset.totalValue);
        final total = totalBank + totalCrypto;
        
        if (total > 0) {
          final cryptoPercentage = (totalCrypto / total) * 100;
          if (cryptoPercentage >= 5 && cryptoPercentage <= 30) {
            score += 30; // İdeal dağılım
          } else if (cryptoPercentage < 50) {
            score += 20; // Kabul edilebilir
          } else {
            score += 10; // Riskli
          }
        }
      }
      
      return score.clamp(0, 100);
    } catch (e) {
      return 0;
    }
  }

  Color _getScoreColor(double score) {
    if (score >= 80) return Colors.green;
    if (score >= 60) return Colors.orange;
    return Colors.red;
  }

  String _getScoreDescription(double score) {
    if (score >= 80) {
      return 'Mükemmel! Finansal sağlığınız çok iyi durumda.';
    } else if (score >= 60) {
      return 'İyi! Bazı iyileştirmeler yapabilirsiniz.';
    } else if (score >= 40) {
      return 'Orta. Finansal planlamanızı gözden geçirin.';
    } else {
      return 'Dikkat! Finansal sağlığınızı iyileştirmeye odaklanın.';
    }
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);
    
    if (difference.inDays == 0) {
      return 'Bugün';
    } else if (difference.inDays == 1) {
      return 'Dün';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} gün önce';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }
}
