import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../services/database_service.dart';
import '../../services/crypto_api_service.dart';
import '../../services/ai_advisor_service.dart';
import '../../theme/app_theme.dart';
import '../../models/bank_account.dart';
import '../../models/crypto_asset.dart';
import '../accounts/accounts_screen.dart';
import '../crypto/crypto_screen.dart';
import '../advisor/advisor_screen.dart';
import '../settings/settings_screen.dart';
import 'widgets/balance_card.dart';
import 'widgets/quick_actions.dart';
import 'widgets/recent_transactions.dart';
import 'widgets/crypto_overview.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> with TickerProviderStateMixin {
  int _currentIndex = 0;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  
  List<BankAccount> _bankAccounts = [];
  List<CryptoAsset> _cryptoAssets = [];
  bool _isLoading = true;

  final List<Widget> _screens = [
    const HomeContent(),
    const AccountsScreen(),
    const CryptoScreen(),
    const AdvisorScreen(),
    const SettingsScreen(),
  ];

  @override
  void initState() {
    super.initState();
    
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    _loadData();
    _animationController.forward();
  }

  Future<void> _loadData() async {
    try {
      final dbService = context.read<DatabaseService>();
      final cryptoService = context.read<CryptoApiService>();
      
      // Verileri yükle
      final accounts = await dbService.getAllBankAccounts();
      final cryptos = await dbService.getAllCryptoAssets();
      
      // Kripto fiyatlarını güncelle
      await cryptoService.fetchCryptoPrices();
      
      setState(() {
        _bankAccounts = accounts;
        _cryptoAssets = cryptos;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: IndexedStack(
        index: _currentIndex,
        children: _screens,
      ),
      bottomNavigationBar: _buildBottomNavigationBar(),
    );
  }

  Widget _buildBottomNavigationBar() {
    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(25),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(25),
        child: BottomNavigationBar(
          currentIndex: _currentIndex,
          onTap: (index) {
            setState(() {
              _currentIndex = index;
            });
          },
          type: BottomNavigationBarType.fixed,
          backgroundColor: Colors.white,
          selectedItemColor: AppTheme.primaryColor,
          unselectedItemColor: Colors.grey.shade600,
          elevation: 0,
          selectedFontSize: 12,
          unselectedFontSize: 10,
          items: const [
            BottomNavigationBarItem(
              icon: Icon(Icons.home_outlined),
              activeIcon: Icon(Icons.home),
              label: 'Ana Sayfa',
            ),
            BottomNavigationBarItem(
              icon: Icon(Icons.account_balance_outlined),
              activeIcon: Icon(Icons.account_balance),
              label: 'Hesaplar',
            ),
            BottomNavigationBarItem(
              icon: Icon(Icons.currency_bitcoin_outlined),
              activeIcon: Icon(Icons.currency_bitcoin),
              label: 'Kripto',
            ),
            BottomNavigationBarItem(
              icon: Icon(Icons.psychology_outlined),
              activeIcon: Icon(Icons.psychology),
              label: 'Danışman',
            ),
            BottomNavigationBarItem(
              icon: Icon(Icons.settings_outlined),
              activeIcon: Icon(Icons.settings),
              label: 'Ayarlar',
            ),
          ],
        ),
      ),
    );
  }
}

class HomeContent extends StatefulWidget {
  const HomeContent({super.key});

  @override
  State<HomeContent> createState() => _HomeContentState();
}

class _HomeContentState extends State<HomeContent> {
  final ScrollController _scrollController = ScrollController();
  bool _isLoading = true;
  List<BankAccount> _bankAccounts = [];
  List<CryptoAsset> _cryptoAssets = [];

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    try {
      final dbService = context.read<DatabaseService>();
      final accounts = await dbService.getAllBankAccounts();
      final cryptos = await dbService.getAllCryptoAssets();
      
      setState(() {
        _bankAccounts = accounts;
        _cryptoAssets = cryptos;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              AppTheme.primaryColor.withValues(alpha: 0.1),
              Colors.white,
            ],
          ),
        ),
        child: SafeArea(
          child: RefreshIndicator(
            onRefresh: _loadData,
            child: CustomScrollView(
              controller: _scrollController,
              physics: const BouncingScrollPhysics(),
              slivers: [
                // App Bar
                SliverAppBar(
                  expandedHeight: 120,
                  floating: true,
                  pinned: false,
                  backgroundColor: Colors.transparent,
                  elevation: 0,
                  flexibleSpace: FlexibleSpaceBar(
                    background: Padding(
                      padding: const EdgeInsets.all(20),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          Text(
                            'Finansal Yönetim',
                            style: TextStyle(
                              fontSize: 28,
                              fontWeight: FontWeight.w900,
                              color: AppTheme.primaryColor,
                              letterSpacing: -1,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            'Akıllı finansal asistanınız',
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.grey.shade600,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                
                // İçerik
                SliverPadding(
                  padding: const EdgeInsets.all(20),
                  sliver: SliverList(
                    delegate: SliverChildListDelegate([
                      if (_isLoading)
                        const Center(
                          child: CircularProgressIndicator(),
                        )
                      else ...[
                        // Bakiye kartı
                        BalanceCard(
                          bankAccounts: _bankAccounts,
                          cryptoAssets: _cryptoAssets,
                        ),
                        
                        const SizedBox(height: 24),
                        
                        // Hızlı işlemler
                        const QuickActions(),
                        
                        const SizedBox(height: 24),
                        
                        // Kripto genel bakış
                        CryptoOverview(cryptoAssets: _cryptoAssets),
                        
                        const SizedBox(height: 24),
                        
                        // Son işlemler
                        const RecentTransactions(),
                        
                        const SizedBox(height: 100), // Bottom navigation için boşluk
                      ],
                    ]),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }
}
