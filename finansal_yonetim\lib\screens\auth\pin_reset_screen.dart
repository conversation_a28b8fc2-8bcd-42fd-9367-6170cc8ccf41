import 'package:flutter/material.dart';
import 'package:pin_code_fields/pin_code_fields.dart';
import 'package:provider/provider.dart';
import '../../services/security_service.dart';
import '../../services/email_service.dart';

class PinResetScreen extends StatefulWidget {
  final VoidCallback onResetSuccess;

  const PinResetScreen({super.key, required this.onResetSuccess});

  @override
  State<PinResetScreen> createState() => _PinResetScreenState();
}

class _PinResetScreenState extends State<PinResetScreen> {
  final TextEditingController _codeController = TextEditingController();
  final TextEditingController _newPinController = TextEditingController();
  final TextEditingController _confirmPinController = TextEditingController();
  
  String _email = "";
  bool _isLoading = false;
  bool _codeSent = false;
  bool _codeVerified = false;
  bool _isSettingNewPin = false;
  String _verificationCode = "";
  String _newPin = "";
  String _confirmPin = "";
  int _remainingTime = 0;

  @override
  void initState() {
    super.initState();
    _loadEmail();
  }

  Future<void> _loadEmail() async {
    final emailService = EmailService();
    final email = await emailService.getSavedEmail();
    setState(() {
      _email = email ?? "";
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('PIN Sıfırlama'),
        centerTitle: true,
      ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Adım göstergesi
              _buildStepIndicator(),
              const SizedBox(height: 32),
              
              Expanded(
                child: _buildCurrentStep(),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStepIndicator() {
    return Row(
      children: [
        _buildStepCircle(1, !_codeSent, _codeSent || _codeVerified),
        Expanded(child: _buildStepLine(_codeSent || _codeVerified)),
        _buildStepCircle(2, _codeSent && !_codeVerified, _codeVerified),
        Expanded(child: _buildStepLine(_codeVerified)),
        _buildStepCircle(3, _codeVerified && !_isSettingNewPin, false),
      ],
    );
  }

  Widget _buildStepCircle(int step, bool isActive, bool isCompleted) {
    return Container(
      width: 32,
      height: 32,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: isCompleted 
            ? Theme.of(context).colorScheme.primary
            : isActive 
                ? Theme.of(context).colorScheme.primaryContainer
                : Theme.of(context).colorScheme.surfaceVariant,
      ),
      child: Center(
        child: isCompleted
            ? Icon(
                Icons.check,
                size: 16,
                color: Theme.of(context).colorScheme.onPrimary,
              )
            : Text(
                '$step',
                style: TextStyle(
                  color: isActive 
                      ? Theme.of(context).colorScheme.onPrimaryContainer
                      : Theme.of(context).colorScheme.onSurfaceVariant,
                  fontWeight: FontWeight.bold,
                ),
              ),
      ),
    );
  }

  Widget _buildStepLine(bool isCompleted) {
    return Container(
      height: 2,
      color: isCompleted 
          ? Theme.of(context).colorScheme.primary
          : Theme.of(context).colorScheme.surfaceVariant,
    );
  }

  Widget _buildCurrentStep() {
    if (!_codeSent) {
      return _buildSendCodeStep();
    } else if (!_codeVerified) {
      return _buildVerifyCodeStep();
    } else {
      return _buildSetNewPinStep();
    }
  }

  Widget _buildSendCodeStep() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(
          Icons.email_outlined,
          size: 80,
          color: Theme.of(context).colorScheme.primary,
        ),
        const SizedBox(height: 24),
        Text(
          'Doğrulama Kodu Gönder',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 16),
        Text(
          'PIN sıfırlama kodu şu e-posta adresine gönderilecek:',
          style: Theme.of(context).textTheme.bodyLarge,
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 8),
        Text(
          _email,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: Theme.of(context).colorScheme.primary,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 32),
        ElevatedButton(
          onPressed: _isLoading ? null : _sendResetCode,
          child: _isLoading
              ? const SizedBox(
                  height: 20,
                  width: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : const Text('Kod Gönder'),
        ),
      ],
    );
  }

  Widget _buildVerifyCodeStep() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(
          Icons.verified_user_outlined,
          size: 80,
          color: Theme.of(context).colorScheme.primary,
        ),
        const SizedBox(height: 24),
        Text(
          'Doğrulama Kodu',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 16),
        Text(
          'E-posta adresinize gönderilen 6 haneli kodu girin',
          style: Theme.of(context).textTheme.bodyLarge,
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 32),
        
        PinCodeTextField(
          appContext: context,
          length: 6,
          controller: _codeController,
          keyboardType: TextInputType.number,
          animationType: AnimationType.fade,
          pinTheme: PinTheme(
            shape: PinCodeFieldShape.box,
            borderRadius: BorderRadius.circular(8),
            fieldHeight: 60,
            fieldWidth: 50,
            activeFillColor: Theme.of(context).colorScheme.primaryContainer,
            inactiveFillColor: Theme.of(context).colorScheme.surfaceVariant,
            selectedFillColor: Theme.of(context).colorScheme.primaryContainer,
          ),
          enableActiveFill: true,
          onCompleted: (value) {
            _verificationCode = value;
            _verifyCode();
          },
          onChanged: (value) {
            _verificationCode = value;
          },
        ),
        
        const SizedBox(height: 24),
        
        if (_remainingTime > 0)
          Text(
            'Yeni kod ${_remainingTime}s sonra gönderilebilir',
            style: Theme.of(context).textTheme.bodySmall,
          )
        else
          TextButton(
            onPressed: _sendResetCode,
            child: const Text('Kodu Tekrar Gönder'),
          ),
      ],
    );
  }

  Widget _buildSetNewPinStep() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(
          Icons.lock_reset,
          size: 80,
          color: Theme.of(context).colorScheme.primary,
        ),
        const SizedBox(height: 24),
        Text(
          'Yeni PIN Oluştur',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 16),
        Text(
          _isSettingNewPin 
              ? 'Yeni PIN\'inizi tekrar girin'
              : 'Yeni 6 haneli PIN\'inizi oluşturun',
          style: Theme.of(context).textTheme.bodyLarge,
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 32),
        
        PinCodeTextField(
          appContext: context,
          length: 6,
          controller: _isSettingNewPin ? _confirmPinController : _newPinController,
          obscureText: true,
          obscuringCharacter: '●',
          animationType: AnimationType.fade,
          pinTheme: PinTheme(
            shape: PinCodeFieldShape.box,
            borderRadius: BorderRadius.circular(8),
            fieldHeight: 60,
            fieldWidth: 50,
            activeFillColor: Theme.of(context).colorScheme.primaryContainer,
            inactiveFillColor: Theme.of(context).colorScheme.surfaceVariant,
            selectedFillColor: Theme.of(context).colorScheme.primaryContainer,
          ),
          enableActiveFill: true,
          onCompleted: (value) {
            if (_isSettingNewPin) {
              _confirmPin = value;
              _setNewPin();
            } else {
              _newPin = value;
              setState(() {
                _isSettingNewPin = true;
              });
            }
          },
          onChanged: (value) {
            if (_isSettingNewPin) {
              _confirmPin = value;
            } else {
              _newPin = value;
            }
          },
        ),
        
        const SizedBox(height: 24),
        
        if (_isSettingNewPin)
          Row(
            children: [
              Expanded(
                child: OutlinedButton(
                  onPressed: () {
                    setState(() {
                      _isSettingNewPin = false;
                      _confirmPinController.clear();
                      _confirmPin = "";
                    });
                  },
                  child: const Text('Geri'),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: ElevatedButton(
                  onPressed: _confirmPin.length == 6 ? _setNewPin : null,
                  child: _isLoading
                      ? const SizedBox(
                          height: 20,
                          width: 20,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : const Text('Onayla'),
                ),
              ),
            ],
          ),
      ],
    );
  }

  Future<void> _sendResetCode() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final emailService = EmailService();
      final success = await emailService.sendResetCode(_email);

      if (success) {
        setState(() {
          _codeSent = true;
        });
        _startCountdown();
        _showSuccessSnackBar('Doğrulama kodu e-posta adresinize gönderildi');
      } else {
        _showErrorDialog('Hata', 'Kod gönderilirken bir hata oluştu.');
      }
    } catch (e) {
      _showErrorDialog('Hata', 'Beklenmeyen bir hata oluştu.');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _verifyCode() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final emailService = EmailService();
      final isValid = await emailService.verifyResetCode(_verificationCode);

      if (isValid) {
        setState(() {
          _codeVerified = true;
        });
      } else {
        _showErrorDialog('Hata', 'Geçersiz veya süresi dolmuş kod.');
        _codeController.clear();
      }
    } catch (e) {
      _showErrorDialog('Hata', 'Kod doğrulanırken bir hata oluştu.');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _setNewPin() async {
    if (_newPin != _confirmPin) {
      _showErrorDialog('Hata', 'PIN\'ler eşleşmiyor.');
      setState(() {
        _isSettingNewPin = false;
        _confirmPinController.clear();
        _confirmPin = "";
      });
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final securityService = context.read<SecurityService>();
      final success = await securityService.setPIN(_newPin);

      if (success) {
        final emailService = EmailService();
        await emailService.completeReset();
        
        _showSuccessDialog();
      } else {
        _showErrorDialog('Hata', 'PIN ayarlanırken bir hata oluştu.');
      }
    } catch (e) {
      _showErrorDialog('Hata', 'Beklenmeyen bir hata oluştu.');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _startCountdown() {
    setState(() {
      _remainingTime = 60;
    });
    
    Future.doWhile(() async {
      await Future.delayed(const Duration(seconds: 1));
      if (mounted) {
        setState(() {
          _remainingTime--;
        });
        return _remainingTime > 0;
      }
      return false;
    });
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Theme.of(context).colorScheme.primary,
      ),
    );
  }

  void _showSuccessDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('Başarılı'),
        content: const Text('PIN\'iniz başarıyla sıfırlandı.'),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              widget.onResetSuccess();
            },
            child: const Text('Tamam'),
          ),
        ],
      ),
    );
  }

  void _showErrorDialog(String title, String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Tamam'),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _codeController.dispose();
    _newPinController.dispose();
    _confirmPinController.dispose();
    super.dispose();
  }
}
