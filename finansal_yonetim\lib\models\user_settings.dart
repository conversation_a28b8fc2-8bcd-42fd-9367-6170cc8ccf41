class UserSettings {
  final int? id;
  final String? email;
  final bool isPinSet;
  final bool biometricEnabled;
  final String currency;
  final bool notificationsEnabled;
  final DateTime createdAt;
  final DateTime updatedAt;

  UserSettings({
    this.id,
    this.email,
    required this.isPinSet,
    required this.biometricEnabled,
    required this.currency,
    required this.notificationsEnabled,
    required this.createdAt,
    required this.updatedAt,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'email': email,
      'is_pin_set': isPinSet ? 1 : 0,
      'biometric_enabled': biometricEnabled ? 1 : 0,
      'currency': currency,
      'notifications_enabled': notificationsEnabled ? 1 : 0,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  factory UserSettings.fromMap(Map<String, dynamic> map) {
    return UserSettings(
      id: map['id'],
      email: map['email'],
      isPinSet: map['is_pin_set'] == 1,
      biometricEnabled: map['biometric_enabled'] == 1,
      currency: map['currency'],
      notificationsEnabled: map['notifications_enabled'] == 1,
      createdAt: DateTime.parse(map['created_at']),
      updatedAt: DateTime.parse(map['updated_at']),
    );
  }

  UserSettings copyWith({
    int? id,
    String? email,
    bool? isPinSet,
    bool? biometricEnabled,
    String? currency,
    bool? notificationsEnabled,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return UserSettings(
      id: id ?? this.id,
      email: email ?? this.email,
      isPinSet: isPinSet ?? this.isPinSet,
      biometricEnabled: biometricEnabled ?? this.biometricEnabled,
      currency: currency ?? this.currency,
      notificationsEnabled: notificationsEnabled ?? this.notificationsEnabled,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}
