import '../models/bank_account.dart';
import '../models/crypto_asset.dart';
import 'database_service.dart';

class AIAdvisorService {
  static final AIAdvisorService _instance = AIAdvisorService._internal();
  factory AIAdvisorService() => _instance;
  AIAdvisorService._internal();

  final DatabaseService _dbService = DatabaseService();

  // Finansal önerileri al
  Future<List<FinancialAdvice>> getFinancialAdvice() async {
    List<FinancialAdvice> advice = [];
    
    final bankAccounts = await _dbService.getAllBankAccounts();
    final cryptoAssets = await _dbService.getAllCryptoAssets();
    
    // Portföy analizi
    advice.addAll(await _analyzePortfolio(bankAccounts, cryptoAssets));
    
    // Risk analizi
    advice.addAll(await _analyzeRisk(bankAccounts, cryptoAssets));
    
    // Diversifikasyon önerileri
    advice.addAll(await _analyzeDiversification(bankAccounts, cryptoAssets));
    
    // Genel finansal sağlık
    advice.addAll(await _analyzeFinancialHealth(bankAccounts, cryptoAssets));
    
    return advice;
  }

  // Portföy analizi
  Future<List<FinancialAdvice>> _analyzePortfolio(
    List<BankAccount> bankAccounts, 
    List<CryptoAsset> cryptoAssets
  ) async {
    List<FinancialAdvice> advice = [];
    
    double totalBank = bankAccounts.fold(0.0, (sum, account) => sum + account.balance);
    double totalCrypto = cryptoAssets.fold(0.0, (sum, asset) => sum + asset.totalValue);
    double totalPortfolio = totalBank + totalCrypto;
    
    if (totalPortfolio == 0) {
      advice.add(FinancialAdvice(
        title: 'Portföy Oluşturma',
        description: 'Finansal yolculuğunuza başlamak için ilk yatırımınızı yapın.',
        type: AdviceType.suggestion,
        priority: AdvicePriority.high,
      ));
      return advice;
    }
    
    double cryptoPercentage = (totalCrypto / totalPortfolio) * 100;
    
    if (cryptoPercentage > 70) {
      advice.add(FinancialAdvice(
        title: 'Yüksek Kripto Riski',
        description: 'Portföyünüzün %${cryptoPercentage.toStringAsFixed(1)}\'i kripto varlıklardan oluşuyor. Risk azaltmak için nakit rezervinizi artırın.',
        type: AdviceType.warning,
        priority: AdvicePriority.high,
      ));
    } else if (cryptoPercentage < 5 && totalCrypto > 0) {
      advice.add(FinancialAdvice(
        title: 'Kripto Diversifikasyonu',
        description: 'Kripto yatırımınız çok düşük. Portföy çeşitliliği için kripto oranını artırmayı düşünün.',
        type: AdviceType.suggestion,
        priority: AdvicePriority.medium,
      ));
    }
    
    return advice;
  }

  // Risk analizi
  Future<List<FinancialAdvice>> _analyzeRisk(
    List<BankAccount> bankAccounts, 
    List<CryptoAsset> cryptoAssets
  ) async {
    List<FinancialAdvice> advice = [];
    
    // Kripto varlık risk analizi
    for (var asset in cryptoAssets) {
      if (asset.profitLossPercentage < -20) {
        advice.add(FinancialAdvice(
          title: '${asset.symbol} Zarar Uyarısı',
          description: '${asset.symbol} yatırımınız %${asset.profitLossPercentage.abs().toStringAsFixed(1)} zarar ediyor. Stop-loss stratejisi düşünün.',
          type: AdviceType.warning,
          priority: AdvicePriority.high,
        ));
      } else if (asset.profitLossPercentage > 50) {
        advice.add(FinancialAdvice(
          title: '${asset.symbol} Kar Realizasyonu',
          description: '${asset.symbol} yatırımınız %${asset.profitLossPercentage.toStringAsFixed(1)} kar ediyor. Kısmi kar realizasyonu düşünebilirsiniz.',
          type: AdviceType.suggestion,
          priority: AdvicePriority.medium,
        ));
      }
    }
    
    // Tek hesap riski
    if (bankAccounts.length == 1) {
      advice.add(FinancialAdvice(
        title: 'Tek Hesap Riski',
        description: 'Tüm paranız tek hesapta. Risk dağıtımı için farklı bankalarda hesap açmayı düşünün.',
        type: AdviceType.suggestion,
        priority: AdvicePriority.medium,
      ));
    }
    
    return advice;
  }

  // Diversifikasyon analizi
  Future<List<FinancialAdvice>> _analyzeDiversification(
    List<BankAccount> bankAccounts, 
    List<CryptoAsset> cryptoAssets
  ) async {
    List<FinancialAdvice> advice = [];
    
    // Kripto çeşitliliği
    if (cryptoAssets.length == 1 && cryptoAssets.isNotEmpty) {
      advice.add(FinancialAdvice(
        title: 'Kripto Çeşitliliği',
        description: 'Sadece ${cryptoAssets.first.symbol} yatırımınız var. Farklı kripto paralar ekleyerek riski dağıtın.',
        type: AdviceType.suggestion,
        priority: AdvicePriority.medium,
      ));
    }
    
    // Para birimi çeşitliliği
    Set<String> currencies = bankAccounts.map((account) => account.currency).toSet();
    if (currencies.length == 1 && currencies.first == 'TRY') {
      advice.add(FinancialAdvice(
        title: 'Para Birimi Çeşitliliği',
        description: 'Tüm paranız TL cinsinden. Döviz kurundaki değişimlere karşı USD/EUR hesap açmayı düşünün.',
        type: AdviceType.suggestion,
        priority: AdvicePriority.low,
      ));
    }
    
    return advice;
  }

  // Finansal sağlık analizi
  Future<List<FinancialAdvice>> _analyzeFinancialHealth(
    List<BankAccount> bankAccounts, 
    List<CryptoAsset> cryptoAssets
  ) async {
    List<FinancialAdvice> advice = [];
    
    double totalBalance = await _dbService.getTotalBalance();
    
    if (totalBalance < 10000) {
      advice.add(FinancialAdvice(
        title: 'Acil Durum Fonu',
        description: 'Acil durum fonu oluşturun. En az 3-6 aylık giderinizi karşılayacak tutarda nakit bulundurun.',
        type: AdviceType.suggestion,
        priority: AdvicePriority.high,
      ));
    }
    
    // Düzenli yatırım önerisi
    advice.add(FinancialAdvice(
      title: 'Düzenli Yatırım',
      description: 'Aylık düzenli yatırım yaparak portföyünüzü büyütün. Küçük miktarlar bile uzun vadede büyük fark yaratır.',
      type: AdviceType.suggestion,
      priority: AdvicePriority.medium,
    ));
    
    return advice;
  }

  // Günlük finansal ipucu
  FinancialAdvice getDailyTip() {
    final tips = [
      FinancialAdvice(
        title: 'Günlük İpucu',
        description: 'Harcamalarınızı takip edin. Küçük harcamalar büyük meblağlara dönüşebilir.',
        type: AdviceType.tip,
        priority: AdvicePriority.low,
      ),
      FinancialAdvice(
        title: 'Günlük İpucu',
        description: 'Yatırım yapmadan önce araştırma yapın. Bilgi en iyi yatırımdır.',
        type: AdviceType.tip,
        priority: AdvicePriority.low,
      ),
      FinancialAdvice(
        title: 'Günlük İpucu',
        description: 'Borçlarınızı öncelikle ödeyin. Faiz ödemek yerine faiz kazanın.',
        type: AdviceType.tip,
        priority: AdvicePriority.low,
      ),
      FinancialAdvice(
        title: 'Günlük İpucu',
        description: 'Portföyünüzü çeşitlendirin. Tüm yumurtalarınızı aynı sepete koymayın.',
        type: AdviceType.tip,
        priority: AdvicePriority.low,
      ),
    ];
    
    final now = DateTime.now();
    final index = now.day % tips.length;
    return tips[index];
  }
}

// Finansal öneri modeli
class FinancialAdvice {
  final String title;
  final String description;
  final AdviceType type;
  final AdvicePriority priority;
  final DateTime createdAt;

  FinancialAdvice({
    required this.title,
    required this.description,
    required this.type,
    required this.priority,
    DateTime? createdAt,
  }) : createdAt = createdAt ?? DateTime.now();
}

enum AdviceType {
  suggestion,
  warning,
  tip,
  alert,
}

enum AdvicePriority {
  low,
  medium,
  high,
}
