import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import '../models/bank_account.dart';
import '../models/crypto_asset.dart';
import '../models/user_settings.dart';

class DatabaseService {
  static final DatabaseService _instance = DatabaseService._internal();
  factory DatabaseService() => _instance;
  DatabaseService._internal();

  static Database? _database;

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    String path = join(await getDatabasesPath(), 'finansal_yonetim.db');
    return await openDatabase(
      path,
      version: 1,
      onCreate: _onCreate,
    );
  }

  Future<void> _onCreate(Database db, int version) async {
    // User Settings tablosu
    await db.execute('''
      CREATE TABLE user_settings(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        email TEXT,
        is_pin_set INTEGER NOT NULL DEFAULT 0,
        biometric_enabled INTEGER NOT NULL DEFAULT 0,
        currency TEXT NOT NULL DEFAULT 'TRY',
        notifications_enabled INTEGER NOT NULL DEFAULT 1,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL
      )
    ''');

    // Bank Accounts tablosu
    await db.execute('''
      CREATE TABLE bank_accounts(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        bank_name TEXT NOT NULL,
        account_number TEXT NOT NULL,
        account_type TEXT NOT NULL,
        balance REAL NOT NULL DEFAULT 0.0,
        currency TEXT NOT NULL DEFAULT 'TRY',
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL
      )
    ''');

    // Crypto Assets tablosu
    await db.execute('''
      CREATE TABLE crypto_assets(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        symbol TEXT NOT NULL,
        name TEXT NOT NULL,
        amount REAL NOT NULL,
        purchase_price REAL NOT NULL,
        current_price REAL NOT NULL,
        purchase_date TEXT NOT NULL,
        updated_at TEXT NOT NULL
      )
    ''');

    // Transactions tablosu (gelecekte kullanım için)
    await db.execute('''
      CREATE TABLE transactions(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        account_id INTEGER,
        crypto_id INTEGER,
        type TEXT NOT NULL,
        amount REAL NOT NULL,
        description TEXT,
        category TEXT,
        date TEXT NOT NULL,
        created_at TEXT NOT NULL,
        FOREIGN KEY (account_id) REFERENCES bank_accounts (id),
        FOREIGN KEY (crypto_id) REFERENCES crypto_assets (id)
      )
    ''');

    // Varsayılan kullanıcı ayarları ekle
    await db.insert('user_settings', {
      'currency': 'TRY',
      'is_pin_set': 0,
      'biometric_enabled': 0,
      'notifications_enabled': 1,
      'created_at': DateTime.now().toIso8601String(),
      'updated_at': DateTime.now().toIso8601String(),
    });
  }

  // User Settings CRUD
  Future<UserSettings?> getUserSettings() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query('user_settings', limit: 1);
    if (maps.isNotEmpty) {
      return UserSettings.fromMap(maps.first);
    }
    return null;
  }

  Future<int> updateUserSettings(UserSettings settings) async {
    final db = await database;
    return await db.update(
      'user_settings',
      settings.toMap(),
      where: 'id = ?',
      whereArgs: [settings.id],
    );
  }

  // Bank Accounts CRUD
  Future<int> insertBankAccount(BankAccount account) async {
    final db = await database;
    return await db.insert('bank_accounts', account.toMap());
  }

  Future<List<BankAccount>> getAllBankAccounts() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query('bank_accounts');
    return List.generate(maps.length, (i) => BankAccount.fromMap(maps[i]));
  }

  Future<int> updateBankAccount(BankAccount account) async {
    final db = await database;
    return await db.update(
      'bank_accounts',
      account.toMap(),
      where: 'id = ?',
      whereArgs: [account.id],
    );
  }

  Future<int> deleteBankAccount(int id) async {
    final db = await database;
    return await db.delete(
      'bank_accounts',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // Crypto Assets CRUD
  Future<int> insertCryptoAsset(CryptoAsset asset) async {
    final db = await database;
    return await db.insert('crypto_assets', asset.toMap());
  }

  Future<List<CryptoAsset>> getAllCryptoAssets() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query('crypto_assets');
    return List.generate(maps.length, (i) => CryptoAsset.fromMap(maps[i]));
  }

  Future<int> updateCryptoAsset(CryptoAsset asset) async {
    final db = await database;
    return await db.update(
      'crypto_assets',
      asset.toMap(),
      where: 'id = ?',
      whereArgs: [asset.id],
    );
  }

  Future<int> deleteCryptoAsset(int id) async {
    final db = await database;
    return await db.delete(
      'crypto_assets',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // Toplam bakiye hesaplama
  Future<double> getTotalBalance() async {
    final bankAccounts = await getAllBankAccounts();
    final cryptoAssets = await getAllCryptoAssets();
    
    double totalBank = bankAccounts.fold(0.0, (sum, account) => sum + account.balance);
    double totalCrypto = cryptoAssets.fold(0.0, (sum, asset) => sum + asset.totalValue);
    
    return totalBank + totalCrypto;
  }
}
