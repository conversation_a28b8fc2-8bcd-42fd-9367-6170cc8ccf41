import 'dart:convert';
import 'dart:math';
import 'package:crypto/crypto.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:local_auth/local_auth.dart';

class SecurityService {
  static final SecurityService _instance = SecurityService._internal();
  factory SecurityService() => _instance;
  SecurityService._internal();

  static const _storage = FlutterSecureStorage();
  static const String _pinKey = 'user_pin_hash';
  static const String _saltKey = 'pin_salt';
  static const String _biometricKey = 'biometric_enabled';

  final LocalAuthentication _localAuth = LocalAuthentication();

  // PIN oluşturma ve kaydetme
  Future<bool> setPIN(String pin) async {
    try {
      // Salt oluştur
      final salt = _generateSalt();
      
      // PIN'i hash'le
      final hashedPin = _hashPIN(pin, salt);
      
      // Güvenli depolamaya kaydet
      await _storage.write(key: _pinKey, value: hashedPin);
      await _storage.write(key: _saltKey, value: salt);
      
      return true;
    } catch (e) {
      return false;
    }
  }

  // PIN doğrulama
  Future<bool> verifyPIN(String pin) async {
    try {
      final storedHash = await _storage.read(key: _pinKey);
      final salt = await _storage.read(key: _saltKey);
      
      if (storedHash == null || salt == null) {
        return false;
      }
      
      final hashedPin = _hashPIN(pin, salt);
      return hashedPin == storedHash;
    } catch (e) {
      return false;
    }
  }

  // PIN'in ayarlanıp ayarlanmadığını kontrol et
  Future<bool> isPINSet() async {
    final storedHash = await _storage.read(key: _pinKey);
    return storedHash != null;
  }

  // PIN'i sil
  Future<bool> deletePIN() async {
    try {
      await _storage.delete(key: _pinKey);
      await _storage.delete(key: _saltKey);
      return true;
    } catch (e) {
      return false;
    }
  }

  // Biyometrik kimlik doğrulama durumunu kontrol et
  Future<bool> isBiometricAvailable() async {
    try {
      final isAvailable = await _localAuth.canCheckBiometrics;
      final isDeviceSupported = await _localAuth.isDeviceSupported();
      return isAvailable && isDeviceSupported;
    } catch (e) {
      return false;
    }
  }

  // Mevcut biyometrik türleri al
  Future<List<BiometricType>> getAvailableBiometrics() async {
    try {
      return await _localAuth.getAvailableBiometrics();
    } catch (e) {
      return [];
    }
  }

  // Biyometrik kimlik doğrulama
  Future<bool> authenticateWithBiometrics() async {
    try {
      final isAvailable = await isBiometricAvailable();
      if (!isAvailable) return false;

      final isAuthenticated = await _localAuth.authenticate(
        localizedReason: 'Uygulamaya erişmek için kimliğinizi doğrulayın',
        options: const AuthenticationOptions(
          biometricOnly: true,
          stickyAuth: true,
        ),
      );

      return isAuthenticated;
    } catch (e) {
      return false;
    }
  }

  // Biyometrik ayarını kaydet
  Future<void> setBiometricEnabled(bool enabled) async {
    await _storage.write(key: _biometricKey, value: enabled.toString());
  }

  // Biyometrik ayarını oku
  Future<bool> isBiometricEnabled() async {
    final value = await _storage.read(key: _biometricKey);
    return value == 'true';
  }

  // Salt oluştur
  String _generateSalt() {
    final random = Random.secure();
    final saltBytes = List<int>.generate(32, (i) => random.nextInt(256));
    return base64.encode(saltBytes);
  }

  // PIN'i hash'le
  String _hashPIN(String pin, String salt) {
    final bytes = utf8.encode(pin + salt);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  // Tüm güvenlik verilerini temizle
  Future<bool> clearAllSecurityData() async {
    try {
      await _storage.delete(key: _pinKey);
      await _storage.delete(key: _saltKey);
      await _storage.delete(key: _biometricKey);
      return true;
    } catch (e) {
      return false;
    }
  }

  // PIN değiştir
  Future<bool> changePIN(String oldPin, String newPin) async {
    try {
      // Önce eski PIN'i doğrula
      final isOldPinValid = await verifyPIN(oldPin);
      if (!isOldPinValid) {
        return false;
      }
      
      // Yeni PIN'i ayarla
      return await setPIN(newPin);
    } catch (e) {
      return false;
    }
  }

  // Güvenlik durumu kontrolü
  Future<Map<String, bool>> getSecurityStatus() async {
    return {
      'isPinSet': await isPINSet(),
      'isBiometricAvailable': await isBiometricAvailable(),
      'isBiometricEnabled': await isBiometricEnabled(),
    };
  }
}
